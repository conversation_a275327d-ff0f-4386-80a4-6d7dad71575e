# Makefile for Actuator Control System

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g
LDFLAGS = -lm

# Source files
SOURCES = actuator_control.c main_example.c
TEST_SOURCES = actuator_control.c test_actuator.c
OBJECTS = $(SOURCES:.c=.o)
TEST_OBJECTS = $(TEST_SOURCES:.c=.o)
TARGET = actuator_control_demo
TEST_TARGET = test_actuator

# Default target
all: $(TARGET) $(TEST_TARGET)

# Build main target
$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $(TARGET) $(LDFLAGS)

# Build test target
$(TEST_TARGET): $(TEST_OBJECTS)
	$(CC) $(TEST_OBJECTS) -o $(TEST_TARGET) $(LDFLAGS)

# Compile source files
%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

# Clean build files
clean:
	rm -f $(OBJECTS) $(TEST_OBJECTS) $(TARGET) $(TEST_TARGET)

# Install dependencies (if needed)
install:
	@echo "No external dependencies required"

# Run the demo
run: $(TARGET)
	./$(TARGET)

# Run tests
test: $(TEST_TARGET)
	./$(TEST_TARGET)

# Run both demo and tests
run-all: run test

# Debug build
debug: CFLAGS += -DDEBUG -g3
debug: $(TARGET) $(TEST_TARGET)

# Release build
release: CFLAGS += -DNDEBUG -O3
release: clean $(TARGET) $(TEST_TARGET)

.PHONY: all clean install run test run-all debug release
