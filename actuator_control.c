#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <math.h>
#include "actuator_config.h"

// 推杆参数定义
#define BACK_MAX_STROKE     144.0f    // 背部最大行程 (mm)
#define LEG_MAX_STROKE      124.0f    // 腿部最大行程 (mm)
#define BACK_MAX_ANGLE      65.0f     // 背部最大角度 (度)
#define LEG_MAX_ANGLE       45.0f     // 腿部最大角度 (度)

// 速度参数 (mm/s)
#define LOAD_SPEED_MIN      3.42f     // 负载最小速度 (3.8 - 10%)
#define LOAD_SPEED_MAX      4.18f     // 负载最大速度 (3.8 + 10%)
#define NOLOAD_SPEED_MIN    3.78f     // 空载最小速度 (4.2 - 10%)
#define NOLOAD_SPEED_MAX    4.62f     // 空载最大速度 (4.2 + 10%)

// 控制参数
#define PWM_MAX             1000      // PWM最大值
#define TIMER_FREQ          1000      // 定时器频率 (Hz)
#define POSITION_TOLERANCE  0.5f      // 位置容差 (mm)

// 推杆类型枚举
typedef enum {
    ACTUATOR_BACK = 0,
    ACTUATOR_LEG = 1
} actuator_type_t;

// 推杆状态结构体
typedef struct {
    actuator_type_t type;           // 推杆类型
    float current_position;         // 当前位置 (mm)
    float target_position;          // 目标位置 (mm)
    float current_angle;            // 当前角度 (度)
    float target_angle;             // 目标角度 (度)
    float speed;                    // 当前速度 (mm/s)
    bool is_moving;                 // 是否在运动
    bool direction;                 // 运动方向 (true=伸出, false=收回)
    uint32_t move_start_time;       // 运动开始时间 (ms)
    uint32_t estimated_time;        // 预估运动时间 (ms)
} actuator_state_t;

// 全局变量
static actuator_state_t back_actuator = {ACTUATOR_BACK, 0, 0, 0, 0, 0, false, true, 0, 0};
static actuator_state_t leg_actuator = {ACTUATOR_LEG, 0, 0, 0, 0, 0, false, true, 0, 0};
static uint32_t system_time_ms = 0;

// 函数声明
float angle_to_position(actuator_type_t type, float angle);
float position_to_angle(actuator_type_t type, float position);
void set_actuator_pwm(actuator_type_t type, uint16_t pwm_value, bool direction);
void update_actuator_position(actuator_state_t* actuator);
bool move_actuator_to_angle(actuator_state_t* actuator, float target_angle, float speed);
void actuator_control_task(void);

/**
 * 角度转换为位置
 * @param type 推杆类型
 * @param angle 角度 (度)
 * @return 位置 (mm)
 */
float angle_to_position(actuator_type_t type, float angle) {
    float max_stroke = (type == ACTUATOR_BACK) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
    float max_angle = (type == ACTUATOR_BACK) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
    
    // 限制角度范围
    if (angle < 0) angle = 0;
    if (angle > max_angle) angle = max_angle;
    
    // 线性映射：角度 -> 位置
    return (angle / max_angle) * max_stroke;
}

/**
 * 位置转换为角度
 * @param type 推杆类型
 * @param position 位置 (mm)
 * @return 角度 (度)
 */
float position_to_angle(actuator_type_t type, float position) {
    float max_stroke = (type == ACTUATOR_BACK) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
    float max_angle = (type == ACTUATOR_BACK) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
    
    // 限制位置范围
    if (position < 0) position = 0;
    if (position > max_stroke) position = max_stroke;
    
    // 线性映射：位置 -> 角度
    return (position / max_stroke) * max_angle;
}

/**
 * 设置推杆PWM输出
 * @param type 推杆类型
 * @param pwm_value PWM值 (0-1000)
 * @param direction 方向 (true=伸出, false=收回)
 */
void set_actuator_pwm(actuator_type_t type, uint16_t pwm_value, bool direction) {
    // 这里需要根据具体硬件实现PWM输出
    // 示例代码，需要根据实际硬件修改
    if (type == ACTUATOR_BACK) {
        // 背部推杆控制
        if (direction) {
            // 伸出方向
            // BACK_PWM_A = pwm_value;
            // BACK_PWM_B = 0;
        } else {
            // 收回方向
            // BACK_PWM_A = 0;
            // BACK_PWM_B = pwm_value;
        }
    } else {
        // 腿部推杆控制
        if (direction) {
            // 伸出方向
            // LEG_PWM_A = pwm_value;
            // LEG_PWM_B = 0;
        } else {
            // 收回方向
            // LEG_PWM_A = 0;
            // LEG_PWM_B = pwm_value;
        }
    }
}

/**
 * 更新推杆位置（基于时间估算）
 * @param actuator 推杆状态指针
 */
void update_actuator_position(actuator_state_t* actuator) {
    if (!actuator->is_moving) return;
    
    uint32_t elapsed_time = system_time_ms - actuator->move_start_time;
    float distance_moved = (actuator->speed * elapsed_time) / 1000.0f;
    
    if (actuator->direction) {
        // 伸出方向
        actuator->current_position = actuator->current_position + distance_moved;
    } else {
        // 收回方向
        actuator->current_position = actuator->current_position - distance_moved;
    }
    
    // 限制位置范围
    float max_stroke = (actuator->type == ACTUATOR_BACK) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
    if (actuator->current_position < 0) actuator->current_position = 0;
    if (actuator->current_position > max_stroke) actuator->current_position = max_stroke;
    
    // 更新当前角度
    actuator->current_angle = position_to_angle(actuator->type, actuator->current_position);
    
    // 检查是否到达目标位置
    float position_error = fabs(actuator->current_position - actuator->target_position);
    if (position_error <= POSITION_TOLERANCE || elapsed_time >= actuator->estimated_time) {
        // 停止运动
        actuator->is_moving = false;
        actuator->current_position = actuator->target_position;
        actuator->current_angle = actuator->target_angle;
        set_actuator_pwm(actuator->type, 0, true);
    }
}

/**
 * 移动推杆到指定角度
 * @param actuator 推杆状态指针
 * @param target_angle 目标角度 (度)
 * @param speed 运动速度 (mm/s)
 * @return 是否成功启动运动
 */
bool move_actuator_to_angle(actuator_state_t* actuator, float target_angle, float speed) {
    // 检查参数有效性
    float max_angle = (actuator->type == ACTUATOR_BACK) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
    if (target_angle < 0 || target_angle > max_angle) return false;
    if (speed <= 0) return false;
    
    // 计算目标位置
    float target_position = angle_to_position(actuator->type, target_angle);
    float distance = fabs(target_position - actuator->current_position);
    
    // 如果已经在目标位置，不需要移动
    if (distance <= POSITION_TOLERANCE) return true;
    
    // 设置运动参数
    actuator->target_angle = target_angle;
    actuator->target_position = target_position;
    actuator->speed = speed;
    actuator->direction = (target_position > actuator->current_position);
    actuator->move_start_time = system_time_ms;
    actuator->estimated_time = (uint32_t)((distance / speed) * 1000);
    actuator->is_moving = true;
    
    // 启动PWM输出
    uint16_t pwm_value = PWM_MAX;  // 可以根据需要调整PWM值
    set_actuator_pwm(actuator->type, pwm_value, actuator->direction);
    
    return true;
}

/**
 * 推杆控制主任务（需要定期调用，建议1ms）
 */
void actuator_control_task(void) {
    system_time_ms++;  // 更新系统时间
    
    // 更新推杆位置
    update_actuator_position(&back_actuator);
    update_actuator_position(&leg_actuator);
}

/**
 * 设置背部角度
 * @param angle 目标角度 (0-65度)
 * @param speed 运动速度 (mm/s)
 * @return 是否成功
 */
bool set_back_angle(float angle, float speed) {
    return move_actuator_to_angle(&back_actuator, angle, speed);
}

/**
 * 设置腿部角度
 * @param angle 目标角度 (0-45度)
 * @param speed 运动速度 (mm/s)
 * @return 是否成功
 */
bool set_leg_angle(float angle, float speed) {
    return move_actuator_to_angle(&leg_actuator, angle, speed);
}

/**
 * 获取背部当前角度
 * @return 当前角度 (度)
 */
float get_back_angle(void) {
    return back_actuator.current_angle;
}

/**
 * 获取腿部当前角度
 * @return 当前角度 (度)
 */
float get_leg_angle(void) {
    return leg_actuator.current_angle;
}

/**
 * 检查推杆是否在运动
 * @param type 推杆类型
 * @return 是否在运动
 */
bool is_actuator_moving(actuator_type_t type) {
    if (type == ACTUATOR_BACK) {
        return back_actuator.is_moving;
    } else {
        return leg_actuator.is_moving;
    }
}

/**
 * 紧急停止所有推杆
 */
void emergency_stop(void) {
    back_actuator.is_moving = false;
    leg_actuator.is_moving = false;
    set_actuator_pwm(ACTUATOR_BACK, 0, true);
    set_actuator_pwm(ACTUATOR_LEG, 0, true);
}

/**
 * 初始化推杆控制系统
 */
void actuator_control_init(void) {
    // 初始化推杆状态
    back_actuator.current_position = 0;
    back_actuator.current_angle = 0;
    leg_actuator.current_position = 0;
    leg_actuator.current_angle = 0;
    
    // 初始化硬件（PWM、GPIO等）
    // 这里需要根据具体硬件平台实现
    
    system_time_ms = 0;
}
