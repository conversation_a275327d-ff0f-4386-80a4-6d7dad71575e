# 基于电流检测的PWM推杆控制原理

## 🔌 核心思想

**电流是负载的直接反映**，通过实时检测电机电流来精确反映负载变化，比基于角度的理论计算更准确、更实用。

## ⚡ 电流-负载关系

### 1. 电机电流组成
```
总电流 = 空载电流 + 负载电流 + 损耗电流

I_total = I_no_load + I_load + I_loss
```

### 2. 电流特性曲线
```
电流(A)
    ↑
6.0 |                    ●  (过载保护)
    |                 ●●●
4.5 |              ●●●     (额定电流)
    |           ●●●
3.0 |        ●●●
    |     ●●●
1.5 |  ●●●
0.8 |●●                     (空载电流)
0.0 |————————————————————————→ 负载
    空载  轻载  中载  重载  过载
```

## 🔧 电流检测实现

### 1. 硬件电路
```
电机 ——[电流传感器]—— PWM驱动器
         |
         ↓
      ADC输入 ——→ 微控制器
```

**常用电流传感器：**
- **霍尔电流传感器** (ACS712, ACS758)
- **分流电阻** + 运放
- **电流互感器**

### 2. ADC采样和转换
```c
// ADC读取
uint16_t adc_value = read_current_adc();

// 转换为电流值
float voltage = (float)adc_value / 4096 * 3.3f;  // 12位ADC, 3.3V参考
float current = voltage / 0.1f;  // 0.1V/A传感器比例
```

### 3. 电流滤波
```c
// 滑动平均滤波
float current_samples[10];
float sum = 0;
for (int i = 0; i < 10; i++) {
    sum += current_samples[i];
}
float average_current = sum / 10;
```

## 📊 负载系数计算

### 基于电流的负载系数
```c
float load_current = average_current - MOTOR_NO_LOAD_CURRENT;
float load_factor = load_current / (MOTOR_RATED_CURRENT - MOTOR_NO_LOAD_CURRENT);

// 限制范围 0-1
if (load_factor < 0) load_factor = 0;
if (load_factor > 1.0f) load_factor = 1.0f;
```

### 负载系数对应表
| 电流(A) | 负载电流(A) | 负载系数 | 负载状态 |
|---------|-------------|----------|----------|
| 0.8     | 0           | 0        | 空载     |
| 1.5     | 0.7         | 0.19     | 轻载     |
| 2.5     | 1.7         | 0.46     | 中载     |
| 3.5     | 2.7         | 0.73     | 重载     |
| 4.5     | 3.7         | 1.0      | 满载     |

## 🎯 速度补偿算法

### 1. 基于电流的速度计算
```c
float current_compensated_pwm_to_speed(float pwm_percent, float load_factor) {
    // 基础速度
    float base_speed = calculate_base_speed(pwm_percent);
    
    // 电流补偿系数
    float current_compensation = 1.0f - (load_factor * 0.4f);
    
    // 实际速度
    return base_speed * current_compensation;
}
```

### 2. 补偿特性
```
速度补偿系数
    ↑
1.0 |●
    | ●
0.8 |  ●
    |   ●
0.6 |    ●————————————————→ 负载系数
    0   0.2  0.4  0.6  0.8  1.0
    
空载时：100%速度
满载时：60%速度（40%衰减）
```

## 🛡️ 保护功能

### 1. 过流保护
```c
if (average_current > MAX_CURRENT_LIMIT) {
    // 立即停止PWM输出
    set_pwm_output(0);
    overcurrent_protection = true;
    
    printf("⚠️ 过流保护触发！电流: %.2f A\n", average_current);
}
```

### 2. 堵转检测
```c
if (current > STALL_CURRENT_THRESHOLD && speed < MIN_SPEED_THRESHOLD) {
    // 检测到堵转
    stall_detected = true;
    reduce_pwm_gradually();
}
```

### 3. 电流限制控制
```c
if (current > RATED_CURRENT * 0.9f) {
    // 接近额定电流时自动降低PWM
    new_pwm = current_pwm * 0.8f;
}
```

## 🔄 自适应控制流程

### 控制循环 (1ms)
```
1. 读取电流ADC值
2. 转换为电流值
3. 滑动平均滤波
4. 计算负载系数
5. 补偿速度计算
6. 时间积分更新位置
7. 检查保护条件
8. 调整PWM输出
```

### 自适应调整 (10ms)
```
1. 评估当前负载状态
2. 根据剩余距离确定目标速度
3. 基于电流反馈计算所需PWM
4. 应用电流限制保护
5. 更新PWM输出
```

## 📈 优势对比

| 方面 | 理论计算 | 电流检测 |
|------|----------|----------|
| **准确性** | 依赖模型精度 | 直接反映真实负载 |
| **适应性** | 固定参数 | 自动适应变化 |
| **实时性** | 预设值 | 实时反馈 |
| **保护功能** | 有限 | 完整的电流保护 |
| **复杂度** | 需要精确建模 | 硬件实现简单 |
| **可靠性** | 受环境影响 | 直接测量可靠 |

## 🔧 实际应用要点

### 1. 硬件选择
- **电流传感器精度**: ±1%以内
- **ADC分辨率**: 12位以上
- **采样频率**: 1kHz以上
- **滤波电路**: 抗干扰设计

### 2. 软件实现
- **滑动平均滤波**: 10个样本
- **异常值检测**: 剔除突变
- **保护阈值**: 留有安全余量
- **响应速度**: 平衡精度和响应

### 3. 校准程序
```c
// 空载电流校准
void calibrate_no_load_current(void) {
    set_pwm(50.0f);  // 中等PWM
    delay(1000);     // 稳定运行
    no_load_current = measure_average_current();
}

// 负载特性校准
void calibrate_load_characteristics(void) {
    for (float load = 0; load <= max_load; load += step) {
        apply_known_load(load);
        measure_current_vs_load();
    }
}
```

## 💡 总结

**基于电流检测的PWM控制**是最实用、最可靠的负载自适应方案：

1. **直接性**: 电流直接反映负载，无需复杂建模
2. **准确性**: 实时测量比理论计算更准确
3. **保护性**: 提供完整的电流保护功能
4. **适应性**: 自动适应各种负载变化
5. **实用性**: 硬件实现相对简单

这种方法特别适合按摩椅推杆这样的应用场景，因为人体负载变化大且难以预测，电流检测能够提供最直接、最可靠的反馈信息。
