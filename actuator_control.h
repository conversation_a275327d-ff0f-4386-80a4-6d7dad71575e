#ifndef ACTUATOR_CONTROL_H
#define ACTUATOR_CONTROL_H

#include <stdint.h>
#include <stdbool.h>

// 推杆类型枚举
typedef enum {
    ACTUATOR_BACK = 0,
    ACTUATOR_LEG = 1
} actuator_type_t;

// 公共函数声明
void actuator_control_init(void);
void actuator_control_task(void);

// 角度控制函数
bool set_back_angle(float angle, float speed);
bool set_leg_angle(float angle, float speed);

// 状态查询函数
float get_back_angle(void);
float get_leg_angle(void);
bool is_actuator_moving(actuator_type_t type);

// 安全函数
void emergency_stop(void);

// 工具函数
float angle_to_position(actuator_type_t type, float angle);
float position_to_angle(actuator_type_t type, float position);

#endif // ACTUATOR_CONTROL_H
