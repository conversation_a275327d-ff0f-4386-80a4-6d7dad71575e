# 推杆电机角度控制系统

基于时间的推杆电机行程角度控制算法，适用于按摩椅等应用场景。

## 系统特性

- **精确角度控制**：基于时间的位置估算算法
- **双推杆支持**：同时控制背部和腿部推杆
- **速度自适应**：支持不同负载条件下的速度调整
- **安全保护**：位置限制和紧急停止功能
- **实时监控**：实时获取推杆位置和角度信息

## 推杆参数

### 背部推杆
- 最大行程：144mm
- 最大角度：65°
- 角度分辨率：约0.45°/mm

### 腿部推杆
- 最大行程：124mm
- 最大角度：45°
- 角度分辨率：约0.36°/mm

### 速度参数
- 负载速度：3.8mm/s ±10%
- 空载速度：4.2mm/s ±10%

## 文件结构

```
├── actuator_control.c    # 主控制算法实现
├── actuator_control.h    # 头文件
├── actuator_config.h     # 配置文件
├── main_example.c        # 使用示例
├── test_actuator.c       # 算法测试程序
├── arduino_example.ino   # Arduino示例程序
├── Makefile             # 编译配置
└── README.md            # 说明文档
```

## 核心算法

### 1. 角度-位置转换
```c
// 角度转位置：position = (angle / max_angle) * max_stroke
float angle_to_position(actuator_type_t type, float angle);

// 位置转角度：angle = (position / max_stroke) * max_angle
float position_to_angle(actuator_type_t type, float position);
```

### 2. 基于时间的位置估算
```c
// 位置更新：position = start_position + speed * elapsed_time
void update_actuator_position(actuator_state_t* actuator);
```

### 3. 运动控制
- 计算目标位置和运动距离
- 估算运动时间
- 启动PWM输出
- 实时监控位置

## API接口

### 初始化函数
```c
void actuator_control_init(void);        // 系统初始化
void actuator_control_task(void);        // 控制任务（1ms调用）
```

### 角度控制函数
```c
bool set_back_angle(float angle, float speed);  // 设置背部角度
bool set_leg_angle(float angle, float speed);   // 设置腿部角度
```

### 状态查询函数
```c
float get_back_angle(void);                     // 获取背部角度
float get_leg_angle(void);                      // 获取腿部角度
bool is_actuator_moving(actuator_type_t type);  // 检查运动状态
```

### 安全函数
```c
void emergency_stop(void);                      // 紧急停止
```

## 使用示例

### 基本控制
```c
// 初始化系统
actuator_control_init();

// 设置背部角度为30度，速度3.8mm/s
set_back_angle(30.0f, 3.8f);

// 设置腿部角度为25度，速度3.8mm/s
set_leg_angle(25.0f, 3.8f);

// 等待运动完成
while (is_actuator_moving(ACTUATOR_BACK) || is_actuator_moving(ACTUATOR_LEG)) {
    actuator_control_task();  // 在定时器中断中调用
    delay_ms(1);
}
```

### 预设位置控制
```c
// 预设位置1：轻微倾斜
set_back_angle(15.0f, 3.8f);
set_leg_angle(10.0f, 3.8f);

// 预设位置2：中等倾斜
set_back_angle(35.0f, 3.8f);
set_leg_angle(25.0f, 3.8f);

// 预设位置3：最大倾斜
set_back_angle(65.0f, 3.8f);
set_leg_angle(45.0f, 3.8f);
```

## 硬件接口

### PWM输出
需要实现以下PWM输出函数：
```c
void set_actuator_pwm(actuator_type_t type, uint16_t pwm_value, bool direction);
```

### 定时器配置
- 频率：1kHz（1ms中断）
- 在中断中调用：`actuator_control_task()`

### GPIO配置
- 背部推杆：2个PWM输出（正转/反转）
- 腿部推杆：2个PWM输出（正转/反转）

## 编译和运行

### 编译
```bash
make                    # 标准编译
make debug             # 调试版本
make release           # 发布版本
```

### 运行示例
```bash
make run               # 编译并运行示例
```

### 运行测试
```bash
make test              # 运行算法测试
make run-all           # 运行演示和测试
```

### 清理
```bash
make clean             # 清理编译文件
```

## Arduino使用

### 硬件连接
- 背部推杆：引脚9(PWM A), 引脚10(PWM B)
- 腿部推杆：引脚11(PWM A), 引脚12(PWM B)
- 使能引脚：引脚13

### 串口命令
- `back <角度>` - 设置背部角度 (0-65度)
- `leg <角度>` - 设置腿部角度 (0-45度)
- `stop` - 紧急停止
- `status` - 显示当前状态
- `help` - 显示帮助

### 使用示例
```
back 30      // 背部推杆移动到30度
leg 25       // 腿部推杆移动到25度
status       // 查看当前角度
stop         // 紧急停止
```

## 移植指南

### 1. 硬件抽象层
修改 `set_actuator_pwm()` 函数以适配具体硬件平台：
- STM32：使用HAL库的PWM输出
- Arduino：使用analogWrite()
- 其他平台：根据具体API修改

### 2. 定时器配置
确保1ms定时器中断调用 `actuator_control_task()`

### 3. 系统时间
如果有RTOS，可以使用系统时钟替代内部计时器

## 性能优化

### 1. 精度提升
- 增加位置反馈传感器（编码器、电位器）
- 实现PID闭环控制
- 动态速度补偿

### 2. 响应速度
- 减少控制周期（0.5ms或更短）
- 优化算法计算复杂度
- 使用查找表替代浮点运算

### 3. 安全性
- 添加限位开关检测
- 实现过流保护
- 增加通信超时检测

## 注意事项

1. **安全第一**：确保在任何情况下都能紧急停止
2. **位置校准**：定期校准零位和最大位置
3. **速度调整**：根据实际负载调整速度参数
4. **温度影响**：考虑温度对电机性能的影响
5. **维护保养**：定期检查机械磨损和电气连接

## 故障排除

### 常见问题
1. **角度不准确**：检查速度参数和机械间隙
2. **运动不平滑**：调整PWM频率和控制周期
3. **无法到达目标位置**：检查机械阻力和电源电压
4. **异常停止**：检查限位开关和过载保护

### 调试方法
1. 使用串口输出调试信息
2. 监控实时位置和速度
3. 记录运动轨迹数据
4. 分析时间-位置曲线
