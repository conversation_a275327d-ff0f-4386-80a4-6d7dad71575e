#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <math.h>
#include <string.h>

// 校准参数定义
#define CALIBRATION_POINTS      5       // 校准点数量
#define CALIBRATION_TOLERANCE   0.2f    // 校准容差 (mm)
#define MAX_CALIBRATION_CYCLES  3       // 最大校准循环次数
#define SETTLING_TIME_MS        2000    // 稳定时间 (ms)

// 校准状态枚举
typedef enum {
    CAL_STATE_IDLE = 0,
    CAL_STATE_ZERO_POSITION,
    CAL_STATE_MAX_POSITION,
    CAL_STATE_SPEED_LEARNING,
    CAL_STATE_LINEARITY_CHECK,
    CAL_STATE_VALIDATION,
    CAL_STATE_COMPLETE,
    CAL_STATE_FAILED
} calibration_state_t;

// 校准数据结构
typedef struct {
    float target_angles[CALIBRATION_POINTS];    // 目标角度
    float actual_angles[CALIBRATION_POINTS];    // 实际角度
    float position_errors[CALIBRATION_POINTS];  // 位置误差
    float speed_samples[20];                    // 速度样本
    int speed_sample_count;                     // 速度样本数
    float learned_speed;                        // 学习到的速度
    float linearity_error;                      // 线性度误差
    bool is_valid;                             // 校准是否有效
} calibration_data_t;

// 校准状态结构
typedef struct {
    calibration_state_t state;
    int current_point;
    int cycle_count;
    uint32_t state_start_time;
    calibration_data_t back_cal;
    calibration_data_t leg_cal;
    bool auto_mode;                            // 自动校准模式
    float (*position_feedback_func)(int actuator_type);  // 位置反馈函数指针
} calibration_system_t;

// 全局校准系统
static calibration_system_t cal_system = {0};

// 外部函数声明（来自主控制系统）
extern bool set_back_angle_adaptive(float angle);
extern bool set_leg_angle_adaptive(float angle);
extern float get_back_angle(void);
extern float get_leg_angle(void);
extern bool is_actuator_moving(int type);
extern void adaptive_control_task(void);

/**
 * 模拟位置传感器读取（实际应用中替换为真实传感器）
 * 在实际系统中，这里应该读取编码器、电位器或其他位置传感器
 */
float read_position_sensor(int actuator_type) {
    // 这里模拟一个带有小误差的位置传感器
    float current_angle;
    if (actuator_type == 0) {  // ACTUATOR_BACK
        current_angle = get_back_angle();
    } else {  // ACTUATOR_LEG
        current_angle = get_leg_angle();
    }
    
    // 模拟传感器噪声 ±0.1°
    float noise = (rand() / (float)RAND_MAX - 0.5f) * 0.2f;
    return current_angle + noise;
}

/**
 * 初始化校准系统
 */
void calibration_init(void) {
    memset(&cal_system, 0, sizeof(cal_system));
    cal_system.state = CAL_STATE_IDLE;
    cal_system.auto_mode = true;
    cal_system.position_feedback_func = read_position_sensor;
    
    // 设置校准点（均匀分布）
    float back_angles[] = {0.0f, 16.25f, 32.5f, 48.75f, 65.0f};
    float leg_angles[] = {0.0f, 11.25f, 22.5f, 33.75f, 45.0f};
    
    memcpy(cal_system.back_cal.target_angles, back_angles, sizeof(back_angles));
    memcpy(cal_system.leg_cal.target_angles, leg_angles, sizeof(leg_angles));
    
    printf("📐 校准系统初始化完成\n");
    printf("背部校准点: ");
    for (int i = 0; i < CALIBRATION_POINTS; i++) {
        printf("%.1f° ", cal_system.back_cal.target_angles[i]);
    }
    printf("\n腿部校准点: ");
    for (int i = 0; i < CALIBRATION_POINTS; i++) {
        printf("%.1f° ", cal_system.leg_cal.target_angles[i]);
    }
    printf("\n");
}

/**
 * 等待推杆稳定
 */
bool wait_for_settling(int actuator_type, uint32_t timeout_ms) {
    uint32_t start_time = cal_system.state_start_time;
    
    while ((get_system_time() - start_time) < timeout_ms) {
        adaptive_control_task();  // 继续控制任务
        
        if (!is_actuator_moving(actuator_type)) {
            // 额外等待稳定时间
            uint32_t settle_start = get_system_time();
            while ((get_system_time() - settle_start) < SETTLING_TIME_MS) {
                adaptive_control_task();
                if (is_actuator_moving(actuator_type)) {
                    break;  // 如果又开始运动，重新等待
                }
            }
            
            if (!is_actuator_moving(actuator_type)) {
                return true;  // 确实稳定了
            }
        }
    }
    
    return false;  // 超时
}

/**
 * 零位校准
 * 将推杆移动到完全收回位置，建立零位基准
 */
bool calibrate_zero_position(int actuator_type) {
    printf("🔄 开始零位校准 - %s推杆\n", 
           actuator_type == 0 ? "背部" : "腿部");
    
    // 移动到零位
    if (actuator_type == 0) {
        set_back_angle_adaptive(0.0f);
    } else {
        set_leg_angle_adaptive(0.0f);
    }
    
    // 等待稳定
    if (!wait_for_settling(actuator_type, 10000)) {
        printf("❌ 零位校准超时\n");
        return false;
    }
    
    // 读取实际位置
    float actual_position = cal_system.position_feedback_func(actuator_type);
    
    printf("✅ 零位校准完成，实际位置: %.2f°\n", actual_position);
    
    // 记录零位偏差
    calibration_data_t* cal_data = (actuator_type == 0) ? 
                                   &cal_system.back_cal : &cal_system.leg_cal;
    cal_data->actual_angles[0] = actual_position;
    cal_data->position_errors[0] = actual_position - 0.0f;
    
    return fabs(actual_position) < CALIBRATION_TOLERANCE;
}

/**
 * 最大位置校准
 * 验证推杆能否到达最大角度
 */
bool calibrate_max_position(int actuator_type) {
    float max_angle = (actuator_type == 0) ? 65.0f : 45.0f;
    
    printf("🔄 开始最大位置校准 - %s推杆 (%.0f°)\n", 
           actuator_type == 0 ? "背部" : "腿部", max_angle);
    
    // 移动到最大位置
    if (actuator_type == 0) {
        set_back_angle_adaptive(max_angle);
    } else {
        set_leg_angle_adaptive(max_angle);
    }
    
    // 等待稳定
    if (!wait_for_settling(actuator_type, 15000)) {
        printf("❌ 最大位置校准超时\n");
        return false;
    }
    
    // 读取实际位置
    float actual_position = cal_system.position_feedback_func(actuator_type);
    
    printf("✅ 最大位置校准完成，实际位置: %.2f°\n", actual_position);
    
    // 记录最大位置偏差
    calibration_data_t* cal_data = (actuator_type == 0) ? 
                                   &cal_system.back_cal : &cal_system.leg_cal;
    cal_data->actual_angles[CALIBRATION_POINTS-1] = actual_position;
    cal_data->position_errors[CALIBRATION_POINTS-1] = actual_position - max_angle;
    
    return fabs(actual_position - max_angle) < CALIBRATION_TOLERANCE;
}

/**
 * 速度学习校准
 * 通过多次运动学习推杆的真实速度特性
 */
bool calibrate_speed_learning(int actuator_type) {
    printf("🔄 开始速度学习校准 - %s推杆\n", 
           actuator_type == 0 ? "背部" : "腿部");
    
    calibration_data_t* cal_data = (actuator_type == 0) ? 
                                   &cal_system.back_cal : &cal_system.leg_cal;
    
    float test_angles[] = {20.0f, 40.0f, 30.0f, 50.0f, 10.0f};
    int num_tests = sizeof(test_angles) / sizeof(test_angles[0]);
    
    cal_data->speed_sample_count = 0;
    
    for (int i = 0; i < num_tests && i < 20; i++) {
        float target_angle = (actuator_type == 0) ? 
                           test_angles[i] : test_angles[i] * 45.0f / 65.0f;
        
        printf("  测试运动 %d: 目标角度 %.1f°\n", i+1, target_angle);
        
        // 记录开始位置和时间
        float start_position = cal_system.position_feedback_func(actuator_type);
        uint32_t start_time = get_system_time();
        
        // 启动运动
        if (actuator_type == 0) {
            set_back_angle_adaptive(target_angle);
        } else {
            set_leg_angle_adaptive(target_angle);
        }
        
        // 等待运动完成
        if (!wait_for_settling(actuator_type, 10000)) {
            printf("    ⚠️ 运动超时\n");
            continue;
        }
        
        // 计算实际速度
        float end_position = cal_system.position_feedback_func(actuator_type);
        uint32_t end_time = get_system_time();
        
        float distance = fabs(end_position - start_position);
        float time_seconds = (end_time - start_time) / 1000.0f;
        
        if (time_seconds > 0.1f) {  // 避免除零
            // 角度转换为位置（mm）
            float max_stroke = (actuator_type == 0) ? 144.0f : 124.0f;
            float max_angle = (actuator_type == 0) ? 65.0f : 45.0f;
            float distance_mm = distance * max_stroke / max_angle;
            
            float speed = distance_mm / time_seconds;
            
            if (speed > 2.0f && speed < 6.0f) {  // 合理范围
                cal_data->speed_samples[cal_data->speed_sample_count] = speed;
                cal_data->speed_sample_count++;
                printf("    ✅ 测得速度: %.2f mm/s\n", speed);
            }
        }
    }
    
    // 计算平均速度
    if (cal_data->speed_sample_count > 0) {
        float sum = 0;
        for (int i = 0; i < cal_data->speed_sample_count; i++) {
            sum += cal_data->speed_samples[i];
        }
        cal_data->learned_speed = sum / cal_data->speed_sample_count;
        
        printf("✅ 速度学习完成，平均速度: %.2f mm/s (样本数: %d)\n", 
               cal_data->learned_speed, cal_data->speed_sample_count);
        return true;
    } else {
        printf("❌ 速度学习失败，无有效样本\n");
        return false;
    }
}

/**
 * 线性度检查校准
 * 检查角度与位置的线性关系是否准确
 */
bool calibrate_linearity_check(int actuator_type) {
    printf("🔄 开始线性度检查 - %s推杆\n", 
           actuator_type == 0 ? "背部" : "腿部");
    
    calibration_data_t* cal_data = (actuator_type == 0) ? 
                                   &cal_system.back_cal : &cal_system.leg_cal;
    
    // 测试中间校准点
    for (int i = 1; i < CALIBRATION_POINTS - 1; i++) {
        float target_angle = cal_data->target_angles[i];
        
        printf("  校准点 %d: 目标角度 %.1f°\n", i+1, target_angle);
        
        // 移动到目标位置
        if (actuator_type == 0) {
            set_back_angle_adaptive(target_angle);
        } else {
            set_leg_angle_adaptive(target_angle);
        }
        
        // 等待稳定
        if (!wait_for_settling(actuator_type, 8000)) {
            printf("    ❌ 运动超时\n");
            return false;
        }
        
        // 读取实际位置
        float actual_angle = cal_system.position_feedback_func(actuator_type);
        cal_data->actual_angles[i] = actual_angle;
        cal_data->position_errors[i] = actual_angle - target_angle;
        
        printf("    实际角度: %.2f°, 误差: %.2f°\n", 
               actual_angle, cal_data->position_errors[i]);
    }
    
    // 计算线性度误差
    float max_error = 0;
    for (int i = 0; i < CALIBRATION_POINTS; i++) {
        if (fabs(cal_data->position_errors[i]) > max_error) {
            max_error = fabs(cal_data->position_errors[i]);
        }
    }
    
    cal_data->linearity_error = max_error;
    
    printf("✅ 线性度检查完成，最大误差: %.2f°\n", max_error);
    
    return max_error < 1.0f;  // 线性度误差小于1度
}

/**
 * 校准验证
 * 随机测试几个角度，验证校准效果
 */
bool calibrate_validation(int actuator_type) {
    printf("🔄 开始校准验证 - %s推杆\n", 
           actuator_type == 0 ? "背部" : "腿部");
    
    float max_angle = (actuator_type == 0) ? 65.0f : 45.0f;
    float test_angles[] = {
        max_angle * 0.15f,  // 15%
        max_angle * 0.35f,  // 35%
        max_angle * 0.65f,  // 65%
        max_angle * 0.85f   // 85%
    };
    
    int passed_tests = 0;
    int total_tests = sizeof(test_angles) / sizeof(test_angles[0]);
    
    for (int i = 0; i < total_tests; i++) {
        float target_angle = test_angles[i];
        
        printf("  验证测试 %d: 目标角度 %.1f°\n", i+1, target_angle);
        
        // 移动到目标位置
        if (actuator_type == 0) {
            set_back_angle_adaptive(target_angle);
        } else {
            set_leg_angle_adaptive(target_angle);
        }
        
        // 等待稳定
        if (!wait_for_settling(actuator_type, 8000)) {
            printf("    ❌ 运动超时\n");
            continue;
        }
        
        // 读取实际位置
        float actual_angle = cal_system.position_feedback_func(actuator_type);
        float error = fabs(actual_angle - target_angle);
        
        printf("    实际角度: %.2f°, 误差: %.2f°", actual_angle, error);
        
        if (error < 0.5f) {
            printf(" ✅\n");
            passed_tests++;
        } else {
            printf(" ❌\n");
        }
    }
    
    float success_rate = (float)passed_tests / total_tests * 100;
    printf("✅ 校准验证完成，成功率: %.0f%% (%d/%d)\n", 
           success_rate, passed_tests, total_tests);
    
    return success_rate >= 75.0f;  // 75%以上通过率
}

/**
 * 自动校准流程
 */
bool auto_calibration(int actuator_type) {
    printf("\n🚀 开始自动校准流程 - %s推杆\n", 
           actuator_type == 0 ? "背部" : "腿部");
    printf("=====================================\n");
    
    calibration_data_t* cal_data = (actuator_type == 0) ? 
                                   &cal_system.back_cal : &cal_system.leg_cal;
    
    // 重置校准数据
    memset(cal_data, 0, sizeof(calibration_data_t));
    
    // 校准步骤
    struct {
        const char* name;
        bool (*func)(int);
        bool required;
    } calibration_steps[] = {
        {"零位校准", calibrate_zero_position, true},
        {"最大位置校准", calibrate_max_position, true},
        {"速度学习校准", calibrate_speed_learning, true},
        {"线性度检查", calibrate_linearity_check, false},
        {"校准验证", calibrate_validation, true}
    };
    
    int num_steps = sizeof(calibration_steps) / sizeof(calibration_steps[0]);
    int passed_steps = 0;
    
    for (int i = 0; i < num_steps; i++) {
        printf("\n📋 步骤 %d: %s\n", i+1, calibration_steps[i].name);
        cal_system.state_start_time = get_system_time();
        
        bool result = calibration_steps[i].func(actuator_type);
        
        if (result) {
            printf("✅ %s 成功\n", calibration_steps[i].name);
            passed_steps++;
        } else {
            printf("❌ %s 失败\n", calibration_steps[i].name);
            if (calibration_steps[i].required) {
                printf("💥 必需步骤失败，校准终止\n");
                cal_data->is_valid = false;
                return false;
            }
        }
    }
    
    // 校准完成
    cal_data->is_valid = (passed_steps >= 4);  // 至少4个步骤成功
    
    printf("\n🎉 自动校准完成！\n");
    printf("成功步骤: %d/%d\n", passed_steps, num_steps);
    printf("校准状态: %s\n", cal_data->is_valid ? "✅ 有效" : "❌ 无效");
    
    return cal_data->is_valid;
}

/**
 * 手动校准模式
 * 允许用户逐步进行校准
 */
void manual_calibration_menu(void) {
    printf("\n📐 手动校准菜单\n");
    printf("================\n");
    printf("1. 背部推杆零位校准\n");
    printf("2. 背部推杆最大位置校准\n");
    printf("3. 背部推杆速度学习\n");
    printf("4. 背部推杆线性度检查\n");
    printf("5. 腿部推杆零位校准\n");
    printf("6. 腿部推杆最大位置校准\n");
    printf("7. 腿部推杆速度学习\n");
    printf("8. 腿部推杆线性度检查\n");
    printf("9. 校准验证\n");
    printf("0. 返回主菜单\n");
    printf("请选择: ");
}

/**
 * 保存校准数据
 * 在实际应用中，应该保存到EEPROM或Flash
 */
void save_calibration_data(void) {
    printf("\n💾 保存校准数据...\n");
    
    // 这里应该将校准数据保存到非易失性存储器
    // 示例：保存到文件（实际应用中替换为EEPROM写入）
    
    printf("背部推杆校准数据:\n");
    printf("  学习速度: %.2f mm/s\n", cal_system.back_cal.learned_speed);
    printf("  线性度误差: %.2f°\n", cal_system.back_cal.linearity_error);
    printf("  校准有效: %s\n", cal_system.back_cal.is_valid ? "是" : "否");
    
    printf("腿部推杆校准数据:\n");
    printf("  学习速度: %.2f mm/s\n", cal_system.leg_cal.learned_speed);
    printf("  线性度误差: %.2f°\n", cal_system.leg_cal.linearity_error);
    printf("  校准有效: %s\n", cal_system.leg_cal.is_valid ? "是" : "否");
    
    printf("✅ 校准数据保存完成\n");
}

/**
 * 加载校准数据
 */
bool load_calibration_data(void) {
    printf("📂 加载校准数据...\n");
    
    // 这里应该从非易失性存储器加载校准数据
    // 示例：从文件加载（实际应用中替换为EEPROM读取）
    
    // 模拟加载的数据
    cal_system.back_cal.learned_speed = 3.75f;
    cal_system.back_cal.linearity_error = 0.3f;
    cal_system.back_cal.is_valid = true;
    
    cal_system.leg_cal.learned_speed = 3.82f;
    cal_system.leg_cal.linearity_error = 0.25f;
    cal_system.leg_cal.is_valid = true;
    
    printf("✅ 校准数据加载完成\n");
    return true;
}

/**
 * 获取系统时间（毫秒）
 * 实际应用中应该使用系统定时器
 */
uint32_t get_system_time(void) {
    static uint32_t time_ms = 0;
    return ++time_ms;  // 简化实现
}

/**
 * 校准系统主函数
 */
int calibration_main(void) {
    printf("🔧 推杆校准系统\n");
    printf("================\n");
    
    calibration_init();
    
    while (1) {
        printf("\n主菜单:\n");
        printf("1. 自动校准（推荐）\n");
        printf("2. 手动校准\n");
        printf("3. 加载校准数据\n");
        printf("4. 保存校准数据\n");
        printf("5. 查看校准状态\n");
        printf("0. 退出\n");
        printf("请选择: ");
        
        int choice;
        scanf("%d", &choice);
        
        switch (choice) {
            case 1:
                printf("选择推杆: 1-背部, 2-腿部, 3-全部: ");
                int actuator;
                scanf("%d", &actuator);
                
                if (actuator == 1 || actuator == 3) {
                    auto_calibration(0);  // 背部
                }
                if (actuator == 2 || actuator == 3) {
                    auto_calibration(1);  // 腿部
                }
                break;
                
            case 2:
                manual_calibration_menu();
                break;
                
            case 3:
                load_calibration_data();
                break;
                
            case 4:
                save_calibration_data();
                break;
                
            case 5:
                printf("\n📊 校准状态:\n");
                printf("背部推杆: %s\n", cal_system.back_cal.is_valid ? "✅ 已校准" : "❌ 未校准");
                printf("腿部推杆: %s\n", cal_system.leg_cal.is_valid ? "✅ 已校准" : "❌ 未校准");
                break;
                
            case 0:
                printf("退出校准系统\n");
                return 0;
                
            default:
                printf("无效选择\n");
                break;
        }
    }
}
