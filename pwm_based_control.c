#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <math.h>

// 推杆参数定义
#define BACK_MAX_STROKE     144.0f    // 背部最大行程 (mm)
#define LEG_MAX_STROKE      124.0f    // 腿部最大行程 (mm)
#define BACK_MAX_ANGLE      65.0f     // 背部最大角度 (度)
#define LEG_MAX_ANGLE       45.0f     // 腿部最大角度 (度)

// PWM参数
#define PWM_MAX             255       // PWM最大值 (8位)
#define PWM_MIN             50        // PWM最小有效值
#define PWM_FREQUENCY       1000      // PWM频率 (Hz)

// 电机特性参数（根据实际测试标定）
#define MOTOR_VOLTAGE       29.0f     // 电机额定电压 (V)
#define MOTOR_NO_LOAD_SPEED 4.2f      // 空载速度 (mm/s) @ 100% PWM
#define MOTOR_LOAD_SPEED    3.8f      // 负载速度 (mm/s) @ 100% PWM
#define MOTOR_STALL_FORCE   6000.0f   // 堵转力 (N)

// 速度-PWM关系参数
#define SPEED_PWM_K1        0.0165f   // 线性系数 (mm/s per PWM)
#define SPEED_PWM_K0        0.0f      // 偏移量
#define PWM_DEAD_ZONE       45        // PWM死区

// 负载补偿参数
#define GRAVITY_LOAD_BACK   800.0f    // 背部重力负载 (N)
#define GRAVITY_LOAD_LEG    600.0f    // 腿部重力负载 (N)
#define FRICTION_COEFF      0.15f     // 摩擦系数

// 推杆类型
typedef enum {
    ACTUATOR_BACK = 0,
    ACTUATOR_LEG = 1
} actuator_type_t;

// PWM控制状态
typedef struct {
    actuator_type_t type;
    float current_position;      // 当前位置 (mm)
    float target_position;       // 目标位置 (mm)
    float current_angle;         // 当前角度 (度)
    float target_angle;          // 目标角度 (度)
    
    // PWM控制参数
    uint16_t current_pwm;        // 当前PWM值
    bool direction;              // 运动方向 (true=伸出, false=收回)
    bool is_moving;              // 是否在运动
    
    // 速度计算
    float calculated_speed;      // 根据PWM计算的速度 (mm/s)
    float load_factor;           // 负载系数
    
    // 时间控制
    uint32_t move_start_time;    // 运动开始时间 (ms)
    uint32_t last_update_time;   // 上次更新时间 (ms)
    
    // 精度控制
    float position_tolerance;    // 位置容差 (mm)
} pwm_actuator_state_t;

// 全局变量
static pwm_actuator_state_t back_actuator = {0};
static pwm_actuator_state_t leg_actuator = {0};
static uint32_t system_time_ms = 0;

/**
 * 角度转换为位置
 */
float angle_to_position(actuator_type_t type, float angle) {
    float max_stroke = (type == ACTUATOR_BACK) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
    float max_angle = (type == ACTUATOR_BACK) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
    
    if (angle < 0) angle = 0;
    if (angle > max_angle) angle = max_angle;
    
    return (angle / max_angle) * max_stroke;
}

/**
 * 位置转换为角度
 */
float position_to_angle(actuator_type_t type, float position) {
    float max_stroke = (type == ACTUATOR_BACK) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
    float max_angle = (type == ACTUATOR_BACK) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
    
    if (position < 0) position = 0;
    if (position > max_stroke) position = max_stroke;
    
    return (position / max_stroke) * max_angle;
}

/**
 * 计算负载系数
 * 根据推杆角度计算当前的负载情况
 */
float calculate_load_factor(actuator_type_t type, float angle, bool direction) {
    float gravity_load = (type == ACTUATOR_BACK) ? GRAVITY_LOAD_BACK : GRAVITY_LOAD_LEG;
    
    // 重力分量随角度变化
    float gravity_component = gravity_load * sin(angle * M_PI / 180.0f);
    
    // 摩擦力
    float friction_force = gravity_load * FRICTION_COEFF;
    
    // 总负载力
    float total_load;
    if (direction) {
        // 伸出方向：对抗重力+摩擦
        total_load = gravity_component + friction_force;
    } else {
        // 收回方向：重力助力-摩擦
        total_load = friction_force - gravity_component;
        if (total_load < 0) total_load = friction_force * 0.5f; // 最小摩擦
    }
    
    // 负载系数 = 实际负载 / 额定负载
    float load_factor = total_load / MOTOR_STALL_FORCE;
    
    // 限制范围
    if (load_factor < 0.1f) load_factor = 0.1f;
    if (load_factor > 0.8f) load_factor = 0.8f;
    
    return load_factor;
}

/**
 * 根据PWM占空比计算实际速度
 * 这是核心算法：PWM -> 速度的精确映射
 */
float pwm_to_speed(uint16_t pwm_value, float load_factor) {
    if (pwm_value < PWM_DEAD_ZONE) {
        return 0.0f; // 死区内无运动
    }
    
    // PWM占空比 (0-1)
    float duty_cycle = (float)pwm_value / PWM_MAX;
    
    // 基础速度（线性关系）
    float base_speed = MOTOR_NO_LOAD_SPEED * duty_cycle;
    
    // 负载补偿
    float load_compensation = 1.0f - (load_factor * 0.3f); // 负载降低30%速度
    
    // 实际速度
    float actual_speed = base_speed * load_compensation;
    
    // 考虑电机特性的非线性修正
    if (duty_cycle < 0.3f) {
        // 低PWM时效率较低
        actual_speed *= (0.7f + 0.3f * duty_cycle / 0.3f);
    }
    
    return actual_speed;
}

/**
 * 根据目标速度计算所需PWM
 * 速度 -> PWM的反向映射
 */
uint16_t speed_to_pwm(float target_speed, float load_factor) {
    if (target_speed <= 0) {
        return 0;
    }
    
    // 考虑负载补偿
    float load_compensation = 1.0f - (load_factor * 0.3f);
    float required_base_speed = target_speed / load_compensation;
    
    // 计算所需占空比
    float required_duty = required_base_speed / MOTOR_NO_LOAD_SPEED;
    
    // 低速非线性补偿
    if (required_duty < 0.3f) {
        required_duty = required_duty / (0.7f + 0.3f * required_duty / 0.3f);
    }
    
    // 转换为PWM值
    uint16_t pwm_value = (uint16_t)(required_duty * PWM_MAX);
    
    // 限制范围
    if (pwm_value < PWM_DEAD_ZONE && pwm_value > 0) {
        pwm_value = PWM_DEAD_ZONE;
    }
    if (pwm_value > PWM_MAX) {
        pwm_value = PWM_MAX;
    }
    
    return pwm_value;
}

/**
 * 设置推杆PWM输出
 */
void set_actuator_pwm(actuator_type_t type, uint16_t pwm_value, bool direction) {
    // 这里应该调用实际的硬件PWM设置函数
    printf("设置%s推杆 PWM: %d, 方向: %s\n", 
           type == ACTUATOR_BACK ? "背部" : "腿部",
           pwm_value, direction ? "伸出" : "收回");
    
    // 更新状态
    pwm_actuator_state_t* actuator = (type == ACTUATOR_BACK) ? &back_actuator : &leg_actuator;
    actuator->current_pwm = pwm_value;
    actuator->direction = direction;
}

/**
 * PWM控制的位置更新算法
 * 核心：根据当前PWM值精确计算位置变化
 */
void pwm_position_update(pwm_actuator_state_t* actuator) {
    if (!actuator->is_moving || actuator->current_pwm == 0) {
        return;
    }
    
    uint32_t current_time = system_time_ms;
    uint32_t elapsed_time = current_time - actuator->last_update_time;
    
    if (elapsed_time == 0) return;
    
    // 计算当前负载系数
    actuator->load_factor = calculate_load_factor(actuator->type, 
                                                 actuator->current_angle, 
                                                 actuator->direction);
    
    // 根据PWM计算实际速度
    actuator->calculated_speed = pwm_to_speed(actuator->current_pwm, actuator->load_factor);
    
    // 计算位置变化
    float distance_moved = (actuator->calculated_speed * elapsed_time) / 1000.0f;
    
    // 更新位置
    if (actuator->direction) {
        actuator->current_position += distance_moved;
    } else {
        actuator->current_position -= distance_moved;
    }
    
    // 位置边界限制
    float max_stroke = (actuator->type == ACTUATOR_BACK) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
    if (actuator->current_position < 0) actuator->current_position = 0;
    if (actuator->current_position > max_stroke) actuator->current_position = max_stroke;
    
    // 更新角度
    actuator->current_angle = position_to_angle(actuator->type, actuator->current_position);
    actuator->last_update_time = current_time;
    
    // 检查是否到达目标
    float position_error = fabs(actuator->current_position - actuator->target_position);
    if (position_error <= actuator->position_tolerance) {
        // 到达目标，停止运动
        actuator->is_moving = false;
        actuator->current_position = actuator->target_position;
        actuator->current_angle = actuator->target_angle;
        set_actuator_pwm(actuator->type, 0, true);
        
        printf("✅ %s推杆到达目标位置 %.1f°\n", 
               actuator->type == ACTUATOR_BACK ? "背部" : "腿部",
               actuator->current_angle);
    }
}

/**
 * 智能PWM控制移动到目标角度
 * 根据距离和精度要求动态调整PWM
 */
bool move_actuator_to_angle_pwm(pwm_actuator_state_t* actuator, float target_angle, float speed_preference) {
    float max_angle = (actuator->type == ACTUATOR_BACK) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
    if (target_angle < 0 || target_angle > max_angle) return false;
    
    float target_position = angle_to_position(actuator->type, target_angle);
    float distance = fabs(target_position - actuator->current_position);
    
    if (distance <= actuator->position_tolerance) return true;
    
    // 设置运动参数
    actuator->target_angle = target_angle;
    actuator->target_position = target_position;
    actuator->direction = (target_position > actuator->current_position);
    actuator->move_start_time = system_time_ms;
    actuator->last_update_time = system_time_ms;
    actuator->is_moving = true;
    
    // 计算初始负载系数
    actuator->load_factor = calculate_load_factor(actuator->type, 
                                                 actuator->current_angle, 
                                                 actuator->direction);
    
    // 根据距离和速度偏好计算PWM
    float target_speed;
    if (distance > 20.0f) {
        // 长距离：使用偏好速度
        target_speed = speed_preference;
    } else if (distance > 5.0f) {
        // 中距离：适中速度
        target_speed = speed_preference * 0.7f;
    } else {
        // 短距离：低速精确控制
        target_speed = speed_preference * 0.4f;
    }
    
    // 计算所需PWM
    uint16_t required_pwm = speed_to_pwm(target_speed, actuator->load_factor);
    
    // 设置PWM输出
    set_actuator_pwm(actuator->type, required_pwm, actuator->direction);
    
    printf("🎯 %s推杆开始移动: %.1f° -> %.1f° (距离:%.1fmm, PWM:%d, 速度:%.2fmm/s)\n",
           actuator->type == ACTUATOR_BACK ? "背部" : "腿部",
           actuator->current_angle, target_angle, distance, required_pwm, target_speed);
    
    return true;
}

/**
 * 动态PWM调整
 * 根据剩余距离动态调整PWM，实现平滑减速
 */
void dynamic_pwm_adjustment(pwm_actuator_state_t* actuator) {
    if (!actuator->is_moving) return;
    
    float remaining_distance = fabs(actuator->target_position - actuator->current_position);
    
    // 动态速度调整策略
    float target_speed;
    if (remaining_distance > 10.0f) {
        // 远距离：保持高速
        target_speed = MOTOR_LOAD_SPEED;
    } else if (remaining_distance > 3.0f) {
        // 中距离：线性减速
        target_speed = MOTOR_LOAD_SPEED * (remaining_distance / 10.0f);
    } else {
        // 近距离：低速精确控制
        target_speed = MOTOR_LOAD_SPEED * 0.3f;
    }
    
    // 最小速度限制
    if (target_speed < MOTOR_LOAD_SPEED * 0.2f) {
        target_speed = MOTOR_LOAD_SPEED * 0.2f;
    }
    
    // 重新计算PWM
    uint16_t new_pwm = speed_to_pwm(target_speed, actuator->load_factor);
    
    // 只有PWM变化较大时才更新
    if (abs(new_pwm - actuator->current_pwm) > 10) {
        set_actuator_pwm(actuator->type, new_pwm, actuator->direction);
    }
}

/**
 * 初始化PWM控制系统
 */
void pwm_actuator_init(void) {
    // 初始化背部推杆
    back_actuator.type = ACTUATOR_BACK;
    back_actuator.current_position = 0;
    back_actuator.current_angle = 0;
    back_actuator.position_tolerance = 0.5f;
    back_actuator.is_moving = false;
    
    // 初始化腿部推杆
    leg_actuator.type = ACTUATOR_LEG;
    leg_actuator.current_position = 0;
    leg_actuator.current_angle = 0;
    leg_actuator.position_tolerance = 0.5f;
    leg_actuator.is_moving = false;
    
    system_time_ms = 0;
    
    printf("🚀 PWM推杆控制系统初始化完成\n");
    printf("控制方式: PWM占空比 -> 速度映射\n");
    printf("精度预期: ±0.2° (基于PWM精确控制)\n");
}

/**
 * PWM控制任务（1ms调用）
 */
void pwm_control_task(void) {
    system_time_ms++;
    
    // 更新位置
    pwm_position_update(&back_actuator);
    pwm_position_update(&leg_actuator);
    
    // 动态PWM调整（每10ms执行一次）
    if (system_time_ms % 10 == 0) {
        dynamic_pwm_adjustment(&back_actuator);
        dynamic_pwm_adjustment(&leg_actuator);
    }
}

/**
 * 设置背部角度（PWM控制版本）
 */
bool set_back_angle_pwm(float angle, float speed) {
    return move_actuator_to_angle_pwm(&back_actuator, angle, speed);
}

/**
 * 设置腿部角度（PWM控制版本）
 */
bool set_leg_angle_pwm(float angle, float speed) {
    return move_actuator_to_angle_pwm(&leg_actuator, angle, speed);
}

/**
 * 获取当前状态
 */
void get_pwm_control_status(void) {
    printf("=== PWM控制状态 ===\n");
    printf("背部推杆:\n");
    printf("  当前角度: %.2f°\n", back_actuator.current_angle);
    printf("  当前PWM: %d (%.1f%%)\n", back_actuator.current_pwm, 
           back_actuator.current_pwm * 100.0f / PWM_MAX);
    printf("  计算速度: %.2f mm/s\n", back_actuator.calculated_speed);
    printf("  负载系数: %.3f\n", back_actuator.load_factor);
    printf("  运动状态: %s\n", back_actuator.is_moving ? "运动中" : "静止");
    
    printf("腿部推杆:\n");
    printf("  当前角度: %.2f°\n", leg_actuator.current_angle);
    printf("  当前PWM: %d (%.1f%%)\n", leg_actuator.current_pwm,
           leg_actuator.current_pwm * 100.0f / PWM_MAX);
    printf("  计算速度: %.2f mm/s\n", leg_actuator.calculated_speed);
    printf("  负载系数: %.3f\n", leg_actuator.load_factor);
    printf("  运动状态: %s\n", leg_actuator.is_moving ? "运动中" : "静止");
}
