#include "actuator_control.h"
#include <stdio.h>
#include <unistd.h>

/**
 * 主程序示例
 * 演示如何使用推杆控制系统
 */
int main(void) {
    printf("推杆控制系统启动...\n");
    
    // 初始化推杆控制系统
    actuator_control_init();
    
    // 模拟定时器中断，每1ms调用一次控制任务
    // 在实际单片机中，这应该在定时器中断中调用
    
    printf("开始角度控制测试...\n");
    
    // 测试1：背部推杆移动到30度
    printf("背部推杆移动到30度...\n");
    if (set_back_angle(30.0f, 3.8f)) {
        printf("背部推杆开始移动\n");
        
        // 等待运动完成
        while (is_actuator_moving(ACTUATOR_BACK)) {
            actuator_control_task();  // 模拟1ms定时器
            usleep(1000);  // 延时1ms
        }
        
        printf("背部推杆当前角度: %.2f度\n", get_back_angle());
    }
    
    // 测试2：腿部推杆移动到25度
    printf("腿部推杆移动到25度...\n");
    if (set_leg_angle(25.0f, 3.8f)) {
        printf("腿部推杆开始移动\n");
        
        // 等待运动完成
        while (is_actuator_moving(ACTUATOR_LEG)) {
            actuator_control_task();  // 模拟1ms定时器
            usleep(1000);  // 延时1ms
        }
        
        printf("腿部推杆当前角度: %.2f度\n", get_leg_angle());
    }
    
    // 测试3：同时控制两个推杆
    printf("同时控制两个推杆...\n");
    set_back_angle(50.0f, 4.0f);
    set_leg_angle(35.0f, 4.0f);
    
    // 等待两个推杆都停止
    while (is_actuator_moving(ACTUATOR_BACK) || is_actuator_moving(ACTUATOR_LEG)) {
        actuator_control_task();
        usleep(1000);
        
        // 打印当前状态
        printf("背部: %.1f度, 腿部: %.1f度\n", 
               get_back_angle(), get_leg_angle());
    }
    
    printf("最终状态 - 背部: %.2f度, 腿部: %.2f度\n", 
           get_back_angle(), get_leg_angle());
    
    // 测试4：回到初始位置
    printf("回到初始位置...\n");
    set_back_angle(0.0f, 3.8f);
    set_leg_angle(0.0f, 3.8f);
    
    while (is_actuator_moving(ACTUATOR_BACK) || is_actuator_moving(ACTUATOR_LEG)) {
        actuator_control_task();
        usleep(1000);
    }
    
    printf("控制测试完成\n");
    return 0;
}

/**
 * 定时器中断服务程序示例（1ms）
 * 在实际单片机中使用
 */
void timer_interrupt_handler(void) {
    actuator_control_task();
}

/**
 * 预设位置控制示例
 */
void preset_positions_example(void) {
    // 预设位置1：轻微倾斜
    printf("预设位置1：轻微倾斜\n");
    set_back_angle(15.0f, 3.8f);
    set_leg_angle(10.0f, 3.8f);
    
    // 预设位置2：中等倾斜
    printf("预设位置2：中等倾斜\n");
    set_back_angle(35.0f, 3.8f);
    set_leg_angle(25.0f, 3.8f);
    
    // 预设位置3：最大倾斜
    printf("预设位置3：最大倾斜\n");
    set_back_angle(65.0f, 3.8f);
    set_leg_angle(45.0f, 3.8f);
}

/**
 * 平滑过渡控制示例
 */
void smooth_transition_example(void) {
    float back_angles[] = {0, 15, 30, 45, 60, 65};
    float leg_angles[] = {0, 10, 20, 30, 40, 45};
    int num_steps = sizeof(back_angles) / sizeof(back_angles[0]);
    
    printf("平滑过渡控制开始...\n");
    
    for (int i = 0; i < num_steps; i++) {
        printf("步骤 %d: 背部%.0f度, 腿部%.0f度\n", 
               i+1, back_angles[i], leg_angles[i]);
        
        set_back_angle(back_angles[i], 3.8f);
        set_leg_angle(leg_angles[i], 3.8f);
        
        // 等待运动完成
        while (is_actuator_moving(ACTUATOR_BACK) || is_actuator_moving(ACTUATOR_LEG)) {
            actuator_control_task();
            usleep(1000);
        }
        
        printf("到达位置 - 背部: %.1f度, 腿部: %.1f度\n", 
               get_back_angle(), get_leg_angle());
        
        // 在每个位置停留2秒
        for (int j = 0; j < 2000; j++) {
            actuator_control_task();
            usleep(1000);
        }
    }
    
    printf("平滑过渡控制完成\n");
}
