#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <math.h>

/**
 * 时间在PWM控制中的作用演示
 * 
 * 核心概念：PWM控制仍然需要时间，但时间的作用是积分计算，而不是主控制变量
 */

// 系统参数
#define CONTROL_PERIOD_MS    1        // 1ms控制周期
#define PWM_MAX             255       // PWM最大值
#define MOTOR_MAX_SPEED     4.2f      // 电机最大速度 (mm/s)

// 全局时间变量
static uint32_t system_time_ms = 0;
static uint32_t last_update_time = 0;

/**
 * 传统时间控制方法（对比用）
 */
typedef struct {
    float target_position;
    float assumed_speed;
    uint32_t estimated_time;
    uint32_t start_time;
    bool is_running;
} traditional_control_t;

/**
 * PWM时间积分控制方法
 */
typedef struct {
    float target_position;
    float current_position;
    uint16_t current_pwm;
    float current_speed;
    uint32_t last_time;
    bool is_running;
} pwm_control_t;

static traditional_control_t trad_ctrl = {0};
static pwm_control_t pwm_ctrl = {0};

/**
 * 模拟系统时间递增
 */
void system_tick(void) {
    system_time_ms++;
}

/**
 * 传统时间控制实现
 */
void traditional_time_control_init(float target_pos) {
    trad_ctrl.target_position = target_pos;
    trad_ctrl.assumed_speed = 3.8f;  // 假设恒定速度
    
    // 关键：基于时间估算
    trad_ctrl.estimated_time = (uint32_t)(target_pos / trad_ctrl.assumed_speed * 1000);
    trad_ctrl.start_time = system_time_ms;
    trad_ctrl.is_running = true;
    
    printf("🕐 传统控制：估算运行时间 %d ms\n", trad_ctrl.estimated_time);
}

void traditional_time_control_update(void) {
    if (!trad_ctrl.is_running) return;
    
    uint32_t elapsed = system_time_ms - trad_ctrl.start_time;
    
    if (elapsed >= trad_ctrl.estimated_time) {
        // 时间到了，假设到达目标
        trad_ctrl.is_running = false;
        printf("🕐 传统控制：时间到达，假设位置 %.2f mm\n", trad_ctrl.target_position);
    }
}

/**
 * PWM控制中的速度计算
 */
float pwm_to_speed(uint16_t pwm_value) {
    if (pwm_value < 50) return 0;  // 死区
    
    float duty_cycle = (float)pwm_value / PWM_MAX;
    return MOTOR_MAX_SPEED * duty_cycle;
}

/**
 * PWM时间积分控制实现
 */
void pwm_time_control_init(float target_pos) {
    pwm_ctrl.target_position = target_pos;
    pwm_ctrl.current_position = 0;
    pwm_ctrl.current_pwm = 200;  // 初始PWM值
    pwm_ctrl.current_speed = pwm_to_speed(pwm_ctrl.current_pwm);
    pwm_ctrl.last_time = system_time_ms;
    pwm_ctrl.is_running = true;
    
    printf("⚡ PWM控制：初始PWM %d, 初始速度 %.2f mm/s\n", 
           pwm_ctrl.current_pwm, pwm_ctrl.current_speed);
}

void pwm_time_control_update(void) {
    if (!pwm_ctrl.is_running) return;
    
    // 关键：时间积分计算位置
    uint32_t current_time = system_time_ms;
    uint32_t elapsed_time = current_time - pwm_ctrl.last_time;
    
    if (elapsed_time > 0) {
        // 1. PWM确定当前速度
        pwm_ctrl.current_speed = pwm_to_speed(pwm_ctrl.current_pwm);
        
        // 2. 时间积分计算位移
        float distance_moved = (pwm_ctrl.current_speed * elapsed_time) / 1000.0f;
        pwm_ctrl.current_position += distance_moved;
        
        // 3. 更新时间基准
        pwm_ctrl.last_time = current_time;
        
        // 4. 动态调整PWM（根据剩余距离）
        float remaining = pwm_ctrl.target_position - pwm_ctrl.current_position;
        if (remaining <= 0.5f) {
            pwm_ctrl.is_running = false;
            pwm_ctrl.current_pwm = 0;
            printf("⚡ PWM控制：到达目标，实际位置 %.2f mm\n", pwm_ctrl.current_position);
        } else if (remaining < 5.0f) {
            // 接近目标，降低PWM
            pwm_ctrl.current_pwm = 100;
        }
    }
}

/**
 * 时间精度对比测试
 */
void time_precision_comparison(void) {
    printf("\n🔬 时间精度对比测试\n");
    printf("====================\n");
    
    float target_distance = 20.0f;  // 目标距离 20mm
    
    // 初始化两种控制方法
    traditional_time_control_init(target_distance);
    pwm_time_control_init(target_distance);
    
    printf("\n时间(ms) | 传统控制状态 | PWM控制位置 | PWM速度 | PWM值\n");
    printf("---------|-------------|-------------|---------|-------\n");
    
    // 模拟运行过程
    for (int i = 0; i < 8000; i++) {
        system_tick();
        traditional_time_control_update();
        pwm_time_control_update();
        
        // 每500ms显示一次状态
        if (i % 500 == 0) {
            printf("  %4d   |    %s     |   %.2f mm   | %.2f mm/s |  %3d\n",
                   system_time_ms,
                   trad_ctrl.is_running ? "运行中" : "已停止",
                   pwm_ctrl.current_position,
                   pwm_ctrl.current_speed,
                   pwm_ctrl.current_pwm);
        }
        
        if (!trad_ctrl.is_running && !pwm_ctrl.is_running) {
            break;
        }
    }
}

/**
 * 时间积分误差分析
 */
void time_integration_error_analysis(void) {
    printf("\n📊 时间积分误差分析\n");
    printf("====================\n");
    
    printf("控制周期对精度的影响：\n\n");
    
    // 模拟不同控制周期的影响
    uint32_t periods[] = {1, 5, 10, 50, 100};  // ms
    
    printf("控制周期 | 积分误差 | 位置精度 | 说明\n");
    printf("---------|----------|----------|------\n");
    
    for (int i = 0; i < 5; i++) {
        uint32_t period = periods[i];
        
        // 模拟积分误差
        float speed = 3.8f;  // mm/s
        float true_distance = speed * 5.0f;  // 5秒运动
        
        // 离散积分误差
        int steps = 5000 / period;
        float discrete_distance = speed * period / 1000.0f * steps;
        float error = fabs(discrete_distance - true_distance);
        float precision = error / true_distance * 100;
        
        const char* comment;
        if (period <= 1) comment = "高精度";
        else if (period <= 10) comment = "良好";
        else if (period <= 50) comment = "可接受";
        else comment = "精度不足";
        
        printf("  %3d ms |  %.3f mm |   %.2f%%   | %s\n",
               period, error, precision, comment);
    }
    
    printf("\n💡 结论：1ms控制周期提供最佳的时间积分精度\n");
}

/**
 * PWM动态调整中的时间作用
 */
void pwm_dynamic_time_demo(void) {
    printf("\n🎛️ PWM动态调整中的时间作用\n");
    printf("============================\n");
    
    printf("演示：PWM根据剩余距离和时间动态调整\n\n");
    
    float target = 30.0f;
    float position = 0.0f;
    uint16_t pwm = 255;
    uint32_t time = 0;
    
    printf("时间(s) | 位置(mm) | 剩余(mm) | PWM值 | 速度(mm/s) | 策略\n");
    printf("--------|----------|----------|-------|-----------|------\n");
    
    while (position < target - 0.1f && time < 10000) {
        // 计算当前速度
        float speed = pwm_to_speed(pwm);
        
        // 时间积分更新位置
        position += speed * 0.1f;  // 100ms步长
        time += 100;
        
        float remaining = target - position;
        
        // 动态PWM调整策略
        const char* strategy;
        if (remaining > 10.0f) {
            pwm = 255;  // 高速
            strategy = "高速接近";
        } else if (remaining > 3.0f) {
            pwm = 150;  // 中速
            strategy = "中速调整";
        } else {
            pwm = 80;   // 低速
            strategy = "低速精确";
        }
        
        if (time % 1000 == 0) {  // 每秒显示
            printf("  %.1f   |  %.2f   |  %.2f   |  %3d  |   %.2f    | %s\n",
                   time/1000.0f, position, remaining, pwm, speed, strategy);
        }
    }
    
    printf("\n✅ 通过时间积分和PWM动态调整，实现精确控制\n");
}

/**
 * 时间同步的重要性演示
 */
void time_synchronization_demo(void) {
    printf("\n⏰ 时间同步的重要性\n");
    printf("==================\n");
    
    printf("对比：准确时间 vs 不准确时间对控制精度的影响\n\n");
    
    float target_distance = 15.0f;
    float speed = 3.0f;  // mm/s
    
    // 准确时间控制
    float accurate_position = 0;
    uint32_t accurate_time = 0;
    
    // 不准确时间控制（时间漂移）
    float inaccurate_position = 0;
    uint32_t inaccurate_time = 0;
    float time_drift = 0;  // 累积时间误差
    
    printf("真实时间 | 准确控制位置 | 漂移控制位置 | 位置误差\n");
    printf("---------|-------------|-------------|----------\n");
    
    for (int i = 0; i < 50; i++) {
        // 真实时间递增
        accurate_time += 100;  // 100ms
        
        // 模拟时间漂移（每次±2ms随机误差）
        float drift = (rand() / (float)RAND_MAX - 0.5f) * 4.0f;
        time_drift += drift;
        inaccurate_time = accurate_time + (uint32_t)time_drift;
        
        // 位置积分计算
        accurate_position += speed * 0.1f;  // 准确的100ms积分
        inaccurate_position += speed * (100 + drift) / 1000.0f;  // 有误差的积分
        
        if (i % 10 == 0) {  // 每秒显示
            float error = fabs(inaccurate_position - accurate_position);
            printf("  %.1f s  |   %.2f mm   |   %.2f mm   | %.3f mm\n",
                   accurate_time/1000.0f, accurate_position, inaccurate_position, error);
        }
        
        if (accurate_position >= target_distance) break;
    }
    
    printf("\n⚠️ 时间不准确会导致位置积分误差累积\n");
}

/**
 * 主程序
 */
int main(void) {
    printf("⏰ PWM控制中时间作用的详细分析\n");
    printf("================================\n\n");
    
    printf("🎯 核心观点：\n");
    printf("PWM控制确实需要时间，但时间的作用是积分计算工具，\n");
    printf("而不是主要的控制变量。PWM确定速度，时间用于积分。\n\n");
    
    // 运行各种演示
    time_precision_comparison();
    time_integration_error_analysis();
    pwm_dynamic_time_demo();
    time_synchronization_demo();
    
    printf("\n📝 总结：\n");
    printf("========\n");
    printf("1. ✅ PWM控制需要时间 - 用于位置积分计算\n");
    printf("2. ⚡ PWM确定瞬时速度 - 这是精度的关键\n");
    printf("3. 🕐 时间是积分工具 - 不是主控制变量\n");
    printf("4. 📊 1ms控制周期 - 保证积分精度\n");
    printf("5. 🎯 动态PWM调整 - 结合时间实现精确控制\n\n");
    
    printf("🔑 关键区别：\n");
    printf("传统方法：时间是主控制变量（估算总时间）\n");
    printf("PWM方法：时间是积分工具（实时计算位移）\n");
    
    return 0;
}
