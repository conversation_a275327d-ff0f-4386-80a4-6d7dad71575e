#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推杆角度控制算法测试程序
演示不同场景下的控制效果
"""

import time
import random
import matplotlib.pyplot as plt
from typing import List, Tu<PERSON>
from actuator_angle_control import ActuatorAngleController, ActuatorType
from actuator_config import ConfigManager, get_scenario_config


class ActuatorSimulator:
    """推杆模拟器，用于测试"""
    
    def __init__(self, controller: ActuatorAngleController):
        self.controller = controller
        self.actual_position = 0.0  # 实际位置
        self.motor_speed = 0.0      # 当前电机速度
        self.last_update_time = time.time()
        
        # 模拟参数
        self.speed_variation = 0.1   # 速度变化范围
        self.position_noise = 0.1    # 位置噪声
        
    def set_motor_speed(self, speed: float):
        """设置电机速度（模拟电机控制）"""
        self.motor_speed = speed
        
    def get_position(self) -> float:
        """获取当前位置（模拟位置传感器）"""
        current_time = time.time()
        dt = current_time - self.last_update_time
        self.last_update_time = current_time
        
        if abs(self.motor_speed) > 0.01:
            # 模拟速度变化
            actual_speed = self.controller.nominal_load_speed * abs(self.motor_speed)
            actual_speed *= (1 + random.uniform(-self.speed_variation, self.speed_variation))
            
            # 更新位置
            if self.motor_speed > 0:
                self.actual_position += actual_speed * dt
            else:
                self.actual_position -= actual_speed * dt
            
            # 限制位置范围
            self.actual_position = max(0, min(self.actual_position, self.controller.max_stroke))
        
        # 添加位置噪声
        noise = random.uniform(-self.position_noise, self.position_noise)
        return self.actual_position + noise


def test_angle_accuracy(controller: ActuatorAngleController, simulator: ActuatorSimulator,
                       test_angles: List[float]) -> List[Tuple[float, float, float]]:
    """测试角度精度
    Args:
        controller: 控制器
        simulator: 模拟器
        test_angles: 测试角度列表
    Returns:
        (目标角度, 实际角度, 误差)的列表
    """
    results = []
    
    # 设置回调函数
    controller.set_motor_control_callback(simulator.set_motor_speed)
    controller.set_position_feedback_callback(simulator.get_position)
    
    print(f"\n=== {controller.actuator_type.value.upper()} 推杆角度精度测试 ===")
    
    for target_angle in test_angles:
        print(f"\n目标角度: {target_angle}°")
        
        start_time = time.time()
        success = controller.move_to_angle(target_angle, load_factor=0.6)
        end_time = time.time()
        
        actual_angle = controller.get_current_angle()
        error = abs(actual_angle - target_angle)
        
        print(f"实际角度: {actual_angle:.2f}°")
        print(f"角度误差: {error:.2f}°")
        print(f"移动时间: {end_time - start_time:.2f}s")
        print(f"移动成功: {'是' if success else '否'}")
        
        results.append((target_angle, actual_angle, error))
        
        # 短暂停顿
        time.sleep(0.5)
    
    return results


def test_speed_adaptation(controller: ActuatorAngleController, simulator: ActuatorSimulator):
    """测试速度自适应"""
    print(f"\n=== {controller.actuator_type.value.upper()} 推杆速度自适应测试 ===")
    
    controller.set_motor_control_callback(simulator.set_motor_speed)
    controller.set_position_feedback_callback(simulator.get_position)
    
    target_angle = controller.max_angle * 0.8  # 80%最大角度
    load_factors = [0.0, 0.3, 0.6, 0.9]  # 不同负载因子
    
    for load_factor in load_factors:
        print(f"\n负载因子: {load_factor}")
        
        # 回到起始位置
        controller.move_to_angle(0, load_factor=0.1)
        time.sleep(0.5)
        
        # 测试移动时间
        start_time = time.time()
        predicted_time = controller.calculate_move_time(0, target_angle, load_factor)
        
        success = controller.move_to_angle(target_angle, load_factor=load_factor)
        actual_time = time.time() - start_time
        
        print(f"预测时间: {predicted_time:.2f}s")
        print(f"实际时间: {actual_time:.2f}s")
        print(f"时间误差: {abs(actual_time - predicted_time):.2f}s")
        print(f"移动成功: {'是' if success else '否'}")


def plot_test_results(back_results: List[Tuple[float, float, float]], 
                     leg_results: List[Tuple[float, float, float]]):
    """绘制测试结果"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 背部推杆结果
    back_targets = [r[0] for r in back_results]
    back_actuals = [r[1] for r in back_results]
    back_errors = [r[2] for r in back_results]
    
    ax1.plot(back_targets, back_actuals, 'bo-', label='实际角度')
    ax1.plot(back_targets, back_targets, 'r--', label='理想角度')
    ax1.set_xlabel('目标角度 (°)')
    ax1.set_ylabel('实际角度 (°)')
    ax1.set_title('背部推杆角度精度')
    ax1.legend()
    ax1.grid(True)
    
    # 腿部推杆结果
    leg_targets = [r[0] for r in leg_results]
    leg_actuals = [r[1] for r in leg_results]
    leg_errors = [r[2] for r in leg_results]
    
    ax2.plot(leg_targets, leg_actuals, 'go-', label='实际角度')
    ax2.plot(leg_targets, leg_targets, 'r--', label='理想角度')
    ax2.set_xlabel('目标角度 (°)')
    ax2.set_ylabel('实际角度 (°)')
    ax2.set_title('腿部推杆角度精度')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('actuator_test_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 误差统计
    print(f"\n=== 精度统计 ===")
    print(f"背部推杆:")
    print(f"  平均误差: {sum(back_errors)/len(back_errors):.3f}°")
    print(f"  最大误差: {max(back_errors):.3f}°")
    print(f"  误差标准差: {(sum([(e - sum(back_errors)/len(back_errors))**2 for e in back_errors])/len(back_errors))**0.5:.3f}°")
    
    print(f"腿部推杆:")
    print(f"  平均误差: {sum(leg_errors)/len(leg_errors):.3f}°")
    print(f"  最大误差: {max(leg_errors):.3f}°")
    print(f"  误差标准差: {(sum([(e - sum(leg_errors)/len(leg_errors))**2 for e in leg_errors])/len(leg_errors))**0.5:.3f}°")


def main():
    """主测试函数"""
    print("推杆角度控制算法测试")
    print("=" * 50)
    
    # 创建控制器
    back_controller = ActuatorAngleController(ActuatorType.BACK)
    leg_controller = ActuatorAngleController(ActuatorType.LEG)
    
    # 创建模拟器
    back_simulator = ActuatorSimulator(back_controller)
    leg_simulator = ActuatorSimulator(leg_controller)
    
    # 测试角度
    back_test_angles = [0, 10, 20, 30, 40, 50, 60, 65]
    leg_test_angles = [0, 5, 10, 15, 20, 30, 40, 45]
    
    # 角度精度测试
    back_results = test_angle_accuracy(back_controller, back_simulator, back_test_angles)
    leg_results = test_angle_accuracy(leg_controller, leg_simulator, leg_test_angles)
    
    # 速度自适应测试
    test_speed_adaptation(back_controller, back_simulator)
    test_speed_adaptation(leg_controller, leg_simulator)
    
    # 绘制结果
    try:
        plot_test_results(back_results, leg_results)
    except ImportError:
        print("\n注意: matplotlib未安装，跳过图表绘制")
    
    # 应用场景演示
    print(f"\n=== 应用场景演示 ===")
    scenario = "massage_chair_back"
    scenario_config = get_scenario_config(scenario)
    
    print(f"场景: {scenario_config['description']}")
    print(f"典型角度: {scenario_config['typical_angles']}")
    
    # 模拟按摩椅调节序列
    for angle in scenario_config['typical_angles'][:5]:  # 测试前5个角度
        success = back_controller.move_to_angle(
            angle, 
            load_factor=scenario_config['default_load_factor']
        )
        print(f"调节到{angle}°: {'成功' if success else '失败'}")
        time.sleep(0.3)


if __name__ == "__main__":
    main()
