#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <math.h>

/**
 * 基于电流检测的PWM推杆控制系统
 * 核心思想：通过实时电流检测来精确反映负载变化
 */

// 推杆参数
#define BACK_MAX_STROKE 144.0f // 背部最大行程 (mm)
#define LEG_MAX_STROKE 124.0f  // 腿部最大行程 (mm)
#define BACK_MAX_ANGLE 65.0f   // 背部最大角度 (度)
#define LEG_MAX_ANGLE 45.0f    // 腿部最大角度 (度)

// PWM参数
#define PWM_MAX 100.0f      // PWM最大占空比 (%)
#define PWM_DEAD_ZONE 15.0f // PWM死区 (%)

// 电机电流参数
#define MOTOR_VOLTAGE 29.0f        // 电机额定电压 (V)
#define MOTOR_NO_LOAD_CURRENT 0.8f // 空载电流 (A)
#define MOTOR_RATED_CURRENT 4.5f   // 额定电流 (A)
#define MOTOR_STALL_CURRENT 8.0f   // 堵转电流 (A)
#define CURRENT_SAMPLE_SIZE 10     // 电流采样窗口大小

// 电机速度-电流特性
#define MOTOR_MAX_SPEED 4.2f // 最大速度 (mm/s) @ 空载
#define MOTOR_MIN_SPEED 0.6f // 最小速度 (mm/s) @ 15% PWM

// 电流检测参数
#define CURRENT_ADC_RESOLUTION 4096 // 12位ADC
#define CURRENT_SENSOR_RATIO 0.1f   // 电流传感器比例 (V/A)
#define ADC_REFERENCE_VOLTAGE 3.3f  // ADC参考电压 (V)

// 推杆控制状态
typedef struct
{
    // 位置和角度
    float current_position; // 当前位置 (mm)
    float target_position;  // 目标位置 (mm)
    float current_angle;    // 当前角度 (度)
    float target_angle;     // 目标角度 (度)

    // PWM控制
    float current_pwm; // 当前PWM占空比 (%)
    bool direction;    // 运动方向 (true=伸出, false=收回)
    bool is_moving;    // 是否在运动

    // 电流检测
    float current_samples[CURRENT_SAMPLE_SIZE]; // 电流采样缓冲区
    int current_sample_index;                   // 当前采样索引
    float average_current;                      // 平均电流 (A)
    float instantaneous_current;                // 瞬时电流 (A)
    float load_current;                         // 负载电流 (A) = 总电流 - 空载电流

    // 速度计算
    float calculated_speed; // 计算速度 (mm/s)
    float load_factor;      // 基于电流的负载系数

    // 时间控制
    uint32_t last_update_time; // 上次更新时间 (ms)
    float position_tolerance;  // 位置容差 (mm)

    // 保护参数
    float max_current_limit;     // 最大电流限制 (A)
    bool overcurrent_protection; // 过流保护状态
} current_based_actuator_t;

static current_based_actuator_t back_actuator = {0};
static current_based_actuator_t leg_actuator = {0};
static uint32_t system_time_ms = 0;

/**
 * 模拟ADC读取电流传感器
 * 实际应用中，这里应该读取真实的ADC值
 */
uint16_t read_current_adc(current_based_actuator_t *actuator)
{
    // 模拟电流值（实际应用中替换为真实ADC读取）
    float simulated_current;

    if (!actuator->is_moving)
    {
        simulated_current = 0.1f; // 静止时的微小电流
    }
    else
    {
        // 基于PWM和负载模拟电流
        float base_current = MOTOR_NO_LOAD_CURRENT +
                             (actuator->current_pwm / 100.0f) * 2.0f;

        // 模拟负载变化（基于角度，但实际中是真实负载）
        float angle_factor = sin(actuator->current_angle * M_PI / 180.0f);
        float load_current = angle_factor * 1.5f; // 最大1.5A负载电流

        simulated_current = base_current + load_current;

        // 添加一些噪声
        float noise = (rand() / (float)RAND_MAX - 0.5f) * 0.2f;
        simulated_current += noise;
    }

    // 转换为ADC值
    float voltage = simulated_current * CURRENT_SENSOR_RATIO;
    uint16_t adc_value = (uint16_t)(voltage / ADC_REFERENCE_VOLTAGE * CURRENT_ADC_RESOLUTION);

    return adc_value;
}

/**
 * ADC值转换为电流值
 */
float adc_to_current(uint16_t adc_value)
{
    float voltage = (float)adc_value / CURRENT_ADC_RESOLUTION * ADC_REFERENCE_VOLTAGE;
    float current = voltage / CURRENT_SENSOR_RATIO;
    return current;
}

/**
 * 电流采样和滤波
 */
void update_current_measurement(current_based_actuator_t *actuator)
{
    // 读取ADC
    uint16_t adc_value = read_current_adc(actuator);

    // 转换为电流
    actuator->instantaneous_current = adc_to_current(adc_value);

    // 存储到采样缓冲区
    actuator->current_samples[actuator->current_sample_index] = actuator->instantaneous_current;
    actuator->current_sample_index = (actuator->current_sample_index + 1) % CURRENT_SAMPLE_SIZE;

    // 计算平均电流（滑动平均滤波）
    float sum = 0;
    for (int i = 0; i < CURRENT_SAMPLE_SIZE; i++)
    {
        sum += actuator->current_samples[i];
    }
    actuator->average_current = sum / CURRENT_SAMPLE_SIZE;

    // 计算负载电流
    actuator->load_current = actuator->average_current - MOTOR_NO_LOAD_CURRENT;
    if (actuator->load_current < 0)
        actuator->load_current = 0;

    // 基于电流计算负载系数
    actuator->load_factor = actuator->load_current / (MOTOR_RATED_CURRENT - MOTOR_NO_LOAD_CURRENT);
    if (actuator->load_factor > 1.0f)
        actuator->load_factor = 1.0f;

    // 过流保护检查
    if (actuator->average_current > actuator->max_current_limit)
    {
        actuator->overcurrent_protection = true;
        printf("⚠️ 过流保护触发！当前电流: %.2f A\n", actuator->average_current);
    }
}

/**
 * 基于电流的PWM到速度映射
 * 电流越大，负载越重，速度越慢
 */
float current_compensated_pwm_to_speed(float pwm_percent, float load_factor)
{
    if (pwm_percent < PWM_DEAD_ZONE)
    {
        return 0.0f;
    }

    // 基础速度计算
    float speed_range = MOTOR_MAX_SPEED - MOTOR_MIN_SPEED;
    float pwm_range = PWM_MAX - PWM_DEAD_ZONE;
    float pwm_normalized = (pwm_percent - PWM_DEAD_ZONE) / pwm_range;
    float base_speed = MOTOR_MIN_SPEED + speed_range * pwm_normalized;

    // 基于实际电流的负载补偿
    // 负载系数越大，速度衰减越多
    float current_compensation = 1.0f - (load_factor * 0.4f); // 最大40%速度衰减

    return base_speed * current_compensation;
}

/**
 * 基于目标速度和当前电流计算所需PWM
 */
float current_compensated_speed_to_pwm(float target_speed, float load_factor)
{
    if (target_speed <= 0)
    {
        return 0.0f;
    }

    // 考虑电流负载补偿
    float current_compensation = 1.0f - (load_factor * 0.4f);
    float required_base_speed = target_speed / current_compensation;

    // 限制速度范围
    if (required_base_speed > MOTOR_MAX_SPEED)
    {
        required_base_speed = MOTOR_MAX_SPEED;
    }

    // 反向计算PWM
    float speed_range = MOTOR_MAX_SPEED - MOTOR_MIN_SPEED;
    float pwm_range = PWM_MAX - PWM_DEAD_ZONE;
    float speed_normalized = (required_base_speed - MOTOR_MIN_SPEED) / speed_range;
    float pwm_percent = PWM_DEAD_ZONE + pwm_range * speed_normalized;

    if (pwm_percent > PWM_MAX)
        pwm_percent = PWM_MAX;

    return pwm_percent;
}

/**
 * 设置PWM输出
 */
void set_current_based_pwm(current_based_actuator_t *actuator, float pwm_percent)
{
    // 过流保护检查
    if (actuator->overcurrent_protection)
    {
        pwm_percent = 0; // 过流时停止输出
    }

    actuator->current_pwm = pwm_percent;

    // 实际硬件PWM设置
    printf("设置PWM: %.1f%%, 电流: %.2f A, 负载系数: %.3f\n",
           pwm_percent, actuator->average_current, actuator->load_factor);
}

/**
 * 位置更新（基于电流补偿的速度）
 */
void update_position_with_current(current_based_actuator_t *actuator)
{
    if (!actuator->is_moving || actuator->current_pwm == 0)
    {
        return;
    }

    uint32_t current_time = system_time_ms;
    uint32_t elapsed_time = current_time - actuator->last_update_time;

    if (elapsed_time == 0)
        return;

    // 更新电流测量
    update_current_measurement(actuator);

    // 基于实际电流计算速度
    actuator->calculated_speed = current_compensated_pwm_to_speed(
        actuator->current_pwm, actuator->load_factor);

    // 时间积分计算位移
    float distance_moved = (actuator->calculated_speed * elapsed_time) / 1000.0f;

    // 更新位置
    if (actuator->direction)
    {
        actuator->current_position += distance_moved;
    }
    else
    {
        actuator->current_position -= distance_moved;
    }

    // 边界限制
    float max_stroke = (actuator == &back_actuator) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
    if (actuator->current_position < 0)
        actuator->current_position = 0;
    if (actuator->current_position > max_stroke)
        actuator->current_position = max_stroke;

    // 更新角度
    float max_angle = (actuator == &back_actuator) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
    actuator->current_angle = (actuator->current_position / max_stroke) * max_angle;

    actuator->last_update_time = current_time;

    // 检查是否到达目标
    float position_error = fabs(actuator->current_position - actuator->target_position);
    if (position_error <= actuator->position_tolerance)
    {
        actuator->is_moving = false;
        actuator->current_position = actuator->target_position;
        actuator->current_angle = actuator->target_angle;
        set_current_based_pwm(actuator, 0.0f);

        printf("✅ 到达目标位置 %.1f°，最终电流: %.2f A\n",
               actuator->current_angle, actuator->average_current);
    }
}

/**
 * 自适应PWM调整（基于电流反馈）
 */
void adaptive_pwm_adjustment(current_based_actuator_t *actuator)
{
    if (!actuator->is_moving)
        return;

    float remaining_distance = fabs(actuator->target_position - actuator->current_position);

    // 基于距离的目标速度
    float target_speed;
    if (remaining_distance > 15.0f)
    {
        target_speed = 3.5f;
    }
    else if (remaining_distance > 5.0f)
    {
        target_speed = 2.0f;
    }
    else
    {
        target_speed = 1.0f;
    }

    // 基于当前电流计算所需PWM
    float new_pwm = current_compensated_speed_to_pwm(target_speed, actuator->load_factor);

    // 电流限制保护
    if (actuator->average_current > MOTOR_RATED_CURRENT * 0.9f)
    {
        // 接近额定电流时降低PWM
        new_pwm *= 0.8f;
        printf("🔄 电流接近限制，降低PWM至 %.1f%%\n", new_pwm);
    }

    // 更新PWM
    if (fabs(new_pwm - actuator->current_pwm) > 3.0f)
    {
        set_current_based_pwm(actuator, new_pwm);
    }
}

/**
 * 移动到目标角度
 */
bool move_to_angle_current_based(current_based_actuator_t *actuator, float target_angle)
{
    float max_angle = (actuator == &back_actuator) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
    float max_stroke = (actuator == &back_actuator) ? BACK_MAX_STROKE : LEG_MAX_STROKE;

    if (target_angle < 0 || target_angle > max_angle)
        return false;

    float target_position = (target_angle / max_angle) * max_stroke;
    float distance = fabs(target_position - actuator->current_position);

    if (distance <= actuator->position_tolerance)
        return true;

    // 重置过流保护
    actuator->overcurrent_protection = false;

    // 设置目标
    actuator->target_angle = target_angle;
    actuator->target_position = target_position;
    actuator->direction = (target_position > actuator->current_position);
    actuator->is_moving = true;
    actuator->last_update_time = system_time_ms;

    // 初始PWM设置（保守值）
    float initial_pwm = 40.0f; // 从较低PWM开始，让电流检测发挥作用
    set_current_based_pwm(actuator, initial_pwm);

    printf("🎯 开始移动: %.1f° -> %.1f° (初始PWM: %.1f%%)\n",
           actuator->current_angle, target_angle, initial_pwm);

    return true;
}

/**
 * 初始化电流检测系统
 */
void current_based_system_init(void)
{
    // 初始化背部推杆
    back_actuator.position_tolerance = 0.5f;
    back_actuator.max_current_limit = 6.0f; // 6A过流保护

    // 初始化腿部推杆
    leg_actuator.position_tolerance = 0.5f;
    leg_actuator.max_current_limit = 6.0f;

    printf("🔌 基于电流检测的PWM控制系统初始化\n");
    printf("电流检测范围: 0-%.1f A\n", MOTOR_STALL_CURRENT);
    printf("过流保护: %.1f A\n", back_actuator.max_current_limit);
    printf("电流采样窗口: %d 个样本\n", CURRENT_SAMPLE_SIZE);
}

/**
 * 控制任务 (1ms调用)
 */
void current_based_control_task(void)
{
    system_time_ms++;

    // 更新位置（包含电流检测）
    update_position_with_current(&back_actuator);
    update_position_with_current(&leg_actuator);

    // 自适应PWM调整 (每10ms)
    if (system_time_ms % 10 == 0)
    {
        adaptive_pwm_adjustment(&back_actuator);
        adaptive_pwm_adjustment(&leg_actuator);
    }
}

/**
 * 电流检测演示
 */
void current_detection_demo(void)
{
    printf("\n📊 电流检测PWM控制演示\n");
    printf("========================\n");

    printf("PWM(%%) | 空载电流(A) | 负载电流(A) | 总电流(A) | 速度(mm/s)\n");
    printf("-------|------------|------------|----------|----------\n");

    float test_pwm[] = {20, 40, 60, 80, 100};

    for (int i = 0; i < 5; i++)
    {
        float pwm = test_pwm[i];

        // 模拟不同负载下的电流
        float no_load_current = MOTOR_NO_LOAD_CURRENT + (pwm / 100.0f) * 1.0f;
        float load_current = (pwm / 100.0f) * 1.5f; // 模拟负载电流
        float total_current = no_load_current + load_current;

        float load_factor = load_current / (MOTOR_RATED_CURRENT - MOTOR_NO_LOAD_CURRENT);
        float speed = current_compensated_pwm_to_speed(pwm, load_factor);

        printf("  %2.0f   |    %.2f     |    %.2f     |   %.2f    |   %.2f\n",
               pwm, no_load_current, load_current, total_current, speed);
    }

    printf("\n💡 电流检测的优势：\n");
    printf("• 实时反映真实负载状态\n");
    printf("• 自动适应负载变化\n");
    printf("• 提供过流保护功能\n");
    printf("• 无需复杂的负载建模\n");
}

/**
 * 主程序
 */
int main(void)
{
    printf("🔌 基于电流检测的PWM推杆控制系统\n");
    printf("=================================\n");

    current_based_system_init();
    current_detection_demo();

    printf("\n🔧 测试电流反馈控制:\n");

    // 测试运动
    move_to_angle_current_based(&back_actuator, 45.0f);

    // 模拟运行
    for (int i = 0; i < 8000; i++)
    {
        current_based_control_task();

        if (i % 1000 == 0)
        {
            printf("时间: %ds, 角度: %.1f°, PWM: %.1f%%, 电流: %.2f A, 速度: %.2f mm/s\n",
                   i / 1000, back_actuator.current_angle, back_actuator.current_pwm,
                   back_actuator.average_current, back_actuator.calculated_speed);
        }

        if (!back_actuator.is_moving)
            break;
    }

    printf("\n✅ 基于电流检测的PWM控制演示完成！\n");
    printf("电流检测提供了最直接、最准确的负载反馈机制。\n");

    return 0;
}
