# 推杆控制系统校准指南

## 📋 校准概述

校准是确保推杆控制系统精度的关键步骤。通过校准，系统可以：
- 建立准确的零位基准
- 学习电机的真实速度特性
- 验证角度-位置映射的线性度
- 补偿机械误差和电气偏差

## 🎯 校准目标

| 参数 | 目标精度 | 验收标准 |
|------|----------|----------|
| 零位精度 | ±0.2° | 必须达到 |
| 最大位置精度 | ±0.2° | 必须达到 |
| 中间位置精度 | ±0.5° | 推荐达到 |
| 速度学习精度 | ±5% | 必须达到 |
| 线性度误差 | <1.0° | 推荐达到 |

## 🔧 校准前准备

### 硬件要求
1. **位置反馈传感器**（推荐）
   - 编码器：精度 ±0.1°
   - 电位器：精度 ±0.2°
   - 霍尔传感器：精度 ±0.3°

2. **机械检查**
   - 推杆安装牢固
   - 传动机构无松动
   - 行程范围正常

3. **电气检查**
   - 供电电压稳定（29V ±5%）
   - 控制信号正常
   - 传感器连接可靠

### 软件准备
```c
// 包含校准系统
#include "calibration_system.h"

// 初始化校准
calibration_init();

// 设置位置反馈函数
set_position_feedback_function(your_sensor_read_function);
```

## 🚀 自动校准流程

### 启动自动校准
```c
// 校准背部推杆
bool result = auto_calibration(ACTUATOR_BACK);

// 校准腿部推杆  
bool result = auto_calibration(ACTUATOR_LEG);
```

### 校准步骤详解

#### 步骤1: 零位校准
**目的：** 建立准确的零位基准

**过程：**
1. 推杆移动到完全收回位置（0°）
2. 等待2秒稳定时间
3. 读取位置传感器数值
4. 记录零位偏差

**判断标准：**
- 实际位置与0°的偏差 < 0.2°
- 推杆完全停止运动

**常见问题：**
- 机械限位不准确 → 调整机械结构
- 传感器零位漂移 → 重新标定传感器

#### 步骤2: 最大位置校准
**目的：** 验证推杆能否到达设计的最大角度

**过程：**
1. 推杆移动到最大角度（背部65°，腿部45°）
2. 等待稳定
3. 读取实际到达位置
4. 计算最大位置误差

**判断标准：**
- 实际位置与目标位置偏差 < 0.2°
- 无机械卡死现象

**常见问题：**
- 行程不足 → 检查机械设计
- 过冲现象 → 调整控制参数

#### 步骤3: 速度学习校准
**目的：** 学习电机在实际工况下的真实速度

**过程：**
```c
// 测试运动序列
float test_angles[] = {20°, 40°, 30°, 50°, 10°};

for (each angle) {
    记录开始位置和时间;
    移动到目标角度;
    等待运动完成;
    记录结束位置和时间;
    计算实际速度 = 距离 / 时间;
}

// 计算平均速度
learned_speed = average(speed_samples);
```

**判断标准：**
- 至少获得3个有效速度样本
- 速度在合理范围内（3.0-5.0 mm/s）
- 速度变异系数 < 15%

**速度计算公式：**
```
距离(mm) = 角度变化 × 最大行程 / 最大角度
速度(mm/s) = 距离(mm) / 时间(s)
```

#### 步骤4: 线性度检查
**目的：** 验证角度与位置的线性映射关系

**过程：**
1. 测试5个均匀分布的校准点
2. 记录每个点的实际位置
3. 计算与理论位置的偏差
4. 分析线性度误差

**校准点分布：**
- 背部：0°, 16.25°, 32.5°, 48.75°, 65°
- 腿部：0°, 11.25°, 22.5°, 33.75°, 45°

**线性度评估：**
```c
for (each calibration point) {
    position_error[i] = actual_angle[i] - target_angle[i];
}
linearity_error = max(abs(position_error[]));
```

#### 步骤5: 校准验证
**目的：** 验证校准效果

**过程：**
1. 随机选择4个测试角度
2. 移动到各测试点
3. 记录位置精度
4. 计算成功率

**验证标准：**
- 位置误差 < 0.5°为成功
- 成功率 ≥ 75%为通过

## 🔧 手动校准模式

### 适用场景
- 自动校准失败时
- 需要单独校准某个参数
- 调试和故障排除

### 手动校准菜单
```
1. 背部推杆零位校准
2. 背部推杆最大位置校准  
3. 背部推杆速度学习
4. 背部推杆线性度检查
5. 腿部推杆零位校准
6. 腿部推杆最大位置校准
7. 腿部推杆速度学习
8. 腿部推杆线性度检查
9. 校准验证
```

### 手动校准步骤
1. 选择要校准的项目
2. 按提示操作
3. 观察校准结果
4. 根据需要重复校准

## 💾 校准数据管理

### 校准数据结构
```c
typedef struct {
    float learned_speed;        // 学习到的速度
    float linearity_error;      // 线性度误差
    float position_errors[5];   // 各校准点误差
    bool is_valid;             // 校准是否有效
    uint32_t calibration_time; // 校准时间戳
} calibration_data_t;
```

### 保存校准数据
```c
// 保存到EEPROM
void save_calibration_to_eeprom(calibration_data_t* data) {
    eeprom_write_block(data, EEPROM_CAL_ADDR, sizeof(calibration_data_t));
}

// 保存到Flash
void save_calibration_to_flash(calibration_data_t* data) {
    flash_write_page(FLASH_CAL_ADDR, (uint8_t*)data, sizeof(calibration_data_t));
}
```

### 加载校准数据
```c
// 从EEPROM加载
bool load_calibration_from_eeprom(calibration_data_t* data) {
    eeprom_read_block(data, EEPROM_CAL_ADDR, sizeof(calibration_data_t));
    return validate_calibration_data(data);
}
```

### 校准数据验证
```c
bool validate_calibration_data(calibration_data_t* data) {
    // 检查速度范围
    if (data->learned_speed < 2.0f || data->learned_speed > 6.0f) {
        return false;
    }
    
    // 检查线性度
    if (data->linearity_error > 2.0f) {
        return false;
    }
    
    // 检查校准标志
    return data->is_valid;
}
```

## 🔍 故障排除

### 常见校准问题

#### 1. 零位校准失败
**症状：** 推杆无法准确到达0°位置

**可能原因：**
- 机械限位位置不准确
- 传动间隙过大
- 控制死区设置不当

**解决方法：**
```c
// 调整控制死区
#define POSITION_TOLERANCE 0.3f  // 放宽容差

// 增加稳定时间
#define SETTLING_TIME_MS 3000    // 延长稳定时间
```

#### 2. 速度学习不稳定
**症状：** 测得的速度值变化很大

**可能原因：**
- 负载变化影响
- 电压不稳定
- 机械摩擦不均匀

**解决方法：**
```c
// 增加测试次数
#define SPEED_LEARNING_CYCLES 10

// 使用中位数滤波
float median_filter(float samples[], int count) {
    sort_array(samples, count);
    return samples[count/2];
}
```

#### 3. 线性度误差过大
**症状：** 中间位置精度差

**可能原因：**
- 机械非线性
- 传感器非线性
- 控制算法问题

**解决方法：**
```c
// 使用分段线性校正
typedef struct {
    float angle_points[10];
    float correction_factors[10];
} linearity_correction_t;

float apply_linearity_correction(float angle) {
    // 查表插值校正
    return interpolate_correction(angle);
}
```

### 校准失败处理

#### 自动重试机制
```c
bool auto_calibration_with_retry(int actuator_type) {
    for (int retry = 0; retry < MAX_RETRY_COUNT; retry++) {
        if (auto_calibration(actuator_type)) {
            return true;  // 校准成功
        }
        
        printf("校准失败，第%d次重试...\n", retry + 1);
        delay_ms(1000);  // 等待1秒后重试
    }
    
    return false;  // 多次重试失败
}
```

#### 降级校准模式
```c
bool fallback_calibration(int actuator_type) {
    // 使用更宽松的标准
    POSITION_TOLERANCE = 0.5f;
    SPEED_TOLERANCE = 10.0f;
    
    // 减少校准步骤
    bool result = basic_calibration(actuator_type);
    
    // 恢复原始标准
    POSITION_TOLERANCE = 0.2f;
    SPEED_TOLERANCE = 5.0f;
    
    return result;
}
```

## 📊 校准报告

### 校准报告格式
```
===============================
推杆校准报告
===============================
校准时间: 2024-01-15 14:30:25
设备型号: 按摩椅推杆控制器 v2.0

背部推杆校准结果:
├── 零位校准: ✅ 通过 (误差: 0.1°)
├── 最大位置: ✅ 通过 (误差: 0.15°)  
├── 速度学习: ✅ 通过 (3.75 mm/s)
├── 线性度检查: ✅ 通过 (最大误差: 0.3°)
└── 校准验证: ✅ 通过 (成功率: 100%)

腿部推杆校准结果:
├── 零位校准: ✅ 通过 (误差: 0.08°)
├── 最大位置: ✅ 通过 (误差: 0.12°)
├── 速度学习: ✅ 通过 (3.82 mm/s)
├── 线性度检查: ✅ 通过 (最大误差: 0.25°)
└── 校准验证: ✅ 通过 (成功率: 100%)

总体评估: ✅ 校准成功
建议: 系统已准备就绪，可投入使用
===============================
```

## 🔄 定期校准建议

### 校准周期
- **首次安装**：必须进行完整校准
- **日常使用**：每月检查校准状态
- **维护保养**：每季度重新校准
- **故障修复**：维修后必须重新校准

### 快速校准检查
```c
bool quick_calibration_check(void) {
    // 测试几个关键位置
    float test_angles[] = {0°, 30°, 60°};
    
    for (int i = 0; i < 3; i++) {
        move_to_angle(test_angles[i]);
        wait_for_settling();
        
        float actual = read_position_sensor();
        if (fabs(actual - test_angles[i]) > 0.5f) {
            return false;  // 需要重新校准
        }
    }
    
    return true;  // 校准仍然有效
}
```

通过这套完整的校准系统，可以确保推杆控制系统在各种工况下都能保持高精度的角度控制。
