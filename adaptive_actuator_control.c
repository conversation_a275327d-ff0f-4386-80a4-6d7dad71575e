#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <math.h>

// 推杆参数定义
#define BACK_MAX_STROKE 144.0f // 背部最大行程 (mm)
#define LEG_MAX_STROKE 124.0f  // 腿部最大行程 (mm)
#define BACK_MAX_ANGLE 65.0f   // 背部最大角度 (度)
#define LEG_MAX_ANGLE 45.0f    // 腿部最大角度 (度)

// 速度自适应参数
#define SPEED_NOMINAL 3.8f      // 标称速度 (mm/s)
#define SPEED_MIN 3.0f          // 最小速度 (mm/s)
#define SPEED_MAX 5.0f          // 最大速度 (mm/s)
#define SPEED_FILTER_ALPHA 0.8f // 速度滤波系数
#define POSITION_TOLERANCE 0.5f // 位置容差 (mm)

// 自适应控制参数
#define ADAPTATION_WINDOW 10   // 自适应窗口大小 (控制周期)
#define MIN_SAMPLES 5          // 最小样本数
#define OUTLIER_THRESHOLD 2.0f // 异常值阈值 (标准差倍数)

// 推杆类型枚举
typedef enum
{
    ACTUATOR_BACK = 0,
    ACTUATOR_LEG = 1
} actuator_type_t;

// 速度估算历史数据
typedef struct
{
    float speed_samples[ADAPTATION_WINDOW];    // 速度样本
    uint32_t time_samples[ADAPTATION_WINDOW];  // 时间样本
    float position_samples[ADAPTATION_WINDOW]; // 位置样本
    int sample_count;                          // 样本数量
    int sample_index;                          // 当前样本索引
    float estimated_speed;                     // 估算速度
    float speed_variance;                      // 速度方差
} speed_estimator_t;

// 推杆状态结构体（增强版）
typedef struct
{
    actuator_type_t type;
    float current_position;      // 当前位置 (mm)
    float target_position;       // 目标位置 (mm)
    float current_angle;         // 当前角度 (度)
    float target_angle;          // 目标角度 (度)
    float nominal_speed;         // 标称速度 (mm/s)
    float actual_speed;          // 实际速度 (mm/s)
    bool is_moving;              // 是否在运动
    bool direction;              // 运动方向
    uint32_t move_start_time;    // 运动开始时间
    uint32_t last_update_time;   // 上次更新时间
    float last_position;         // 上次位置
    speed_estimator_t speed_est; // 速度估算器

    // 自适应控制参数
    float position_error;          // 位置误差
    float cumulative_error;        // 累积误差
    float speed_correction_factor; // 速度校正因子
    bool adaptive_enabled;         // 是否启用自适应
} adaptive_actuator_state_t;

// 全局变量
static adaptive_actuator_state_t back_actuator = {0};
static adaptive_actuator_state_t leg_actuator = {0};
static uint32_t system_time_ms = 0;

// 函数声明
float angle_to_position(actuator_type_t type, float angle);
float position_to_angle(actuator_type_t type, float position);
void init_speed_estimator(speed_estimator_t *estimator);
void update_speed_estimator(speed_estimator_t *estimator, float position, uint32_t time);
float get_estimated_speed(speed_estimator_t *estimator);
void adaptive_position_update(adaptive_actuator_state_t *actuator);
bool move_actuator_to_angle_adaptive(adaptive_actuator_state_t *actuator, float target_angle);

/**
 * 角度转换为位置
 */
float angle_to_position(actuator_type_t type, float angle)
{
    float max_stroke = (type == ACTUATOR_BACK) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
    float max_angle = (type == ACTUATOR_BACK) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;

    if (angle < 0)
        angle = 0;
    if (angle > max_angle)
        angle = max_angle;

    return (angle / max_angle) * max_stroke;
}

/**
 * 位置转换为角度
 */
float position_to_angle(actuator_type_t type, float position)
{
    float max_stroke = (type == ACTUATOR_BACK) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
    float max_angle = (type == ACTUATOR_BACK) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;

    if (position < 0)
        position = 0;
    if (position > max_stroke)
        position = max_stroke;

    return (position / max_stroke) * max_angle;
}

/**
 * 初始化速度估算器
 */
void init_speed_estimator(speed_estimator_t *estimator)
{
    estimator->sample_count = 0;
    estimator->sample_index = 0;
    estimator->estimated_speed = SPEED_NOMINAL;
    estimator->speed_variance = 0.0f;

    for (int i = 0; i < ADAPTATION_WINDOW; i++)
    {
        estimator->speed_samples[i] = SPEED_NOMINAL;
        estimator->time_samples[i] = 0;
        estimator->position_samples[i] = 0;
    }
}

/**
 * 更新速度估算器
 * 核心自适应算法：基于实际位置变化来估算真实速度
 */
void update_speed_estimator(speed_estimator_t *estimator, float position, uint32_t time)
{
    // 添加新样本
    estimator->position_samples[estimator->sample_index] = position;
    estimator->time_samples[estimator->sample_index] = time;

    // 如果有足够的样本，计算瞬时速度
    if (estimator->sample_count > 0)
    {
        int prev_index = (estimator->sample_index - 1 + ADAPTATION_WINDOW) % ADAPTATION_WINDOW;
        float delta_position = position - estimator->position_samples[prev_index];
        uint32_t delta_time = time - estimator->time_samples[prev_index];

        if (delta_time > 0)
        {
            float instantaneous_speed = fabs(delta_position) / (delta_time / 1000.0f);

            // 异常值检测和过滤
            if (instantaneous_speed >= SPEED_MIN && instantaneous_speed <= SPEED_MAX)
            {
                estimator->speed_samples[estimator->sample_index] = instantaneous_speed;
            }
            else
            {
                // 使用上一个有效值
                estimator->speed_samples[estimator->sample_index] =
                    estimator->speed_samples[prev_index];
            }
        }
    }

    // 更新索引和计数
    estimator->sample_index = (estimator->sample_index + 1) % ADAPTATION_WINDOW;
    if (estimator->sample_count < ADAPTATION_WINDOW)
    {
        estimator->sample_count++;
    }

    // 计算加权平均速度（如果有足够样本）
    if (estimator->sample_count >= MIN_SAMPLES)
    {
        float sum = 0.0f;
        float sum_squares = 0.0f;
        int valid_samples = 0;

        // 计算平均值
        for (int i = 0; i < estimator->sample_count; i++)
        {
            sum += estimator->speed_samples[i];
            valid_samples++;
        }

        if (valid_samples > 0)
        {
            float mean_speed = sum / valid_samples;

            // 计算方差
            for (int i = 0; i < valid_samples; i++)
            {
                float diff = estimator->speed_samples[i] - mean_speed;
                sum_squares += diff * diff;
            }
            estimator->speed_variance = sum_squares / valid_samples;

            // 使用指数移动平均更新估算速度
            estimator->estimated_speed = SPEED_FILTER_ALPHA * estimator->estimated_speed +
                                         (1.0f - SPEED_FILTER_ALPHA) * mean_speed;
        }
    }
}

/**
 * 获取估算速度
 */
float get_estimated_speed(speed_estimator_t *estimator)
{
    return estimator->estimated_speed;
}

/**
 * 自适应位置更新算法
 * 这是核心：根据实际运行情况动态调整位置估算
 */
void adaptive_position_update(adaptive_actuator_state_t *actuator)
{
    if (!actuator->is_moving)
        return;

    uint32_t current_time = system_time_ms;
    uint32_t elapsed_time = current_time - actuator->last_update_time;

    if (elapsed_time == 0)
        return;

    // 方法1：基于标称速度的位置估算
    float nominal_distance = (actuator->nominal_speed * elapsed_time) / 1000.0f;

    // 方法2：基于自适应速度的位置估算
    float adaptive_distance = (actuator->actual_speed * elapsed_time) / 1000.0f;

    // 方法3：基于速度校正因子的位置估算
    float corrected_distance = nominal_distance * actuator->speed_correction_factor;

    // 选择最佳估算方法
    float estimated_distance;
    if (actuator->adaptive_enabled && actuator->speed_est.sample_count >= MIN_SAMPLES)
    {
        // 使用自适应速度
        estimated_distance = adaptive_distance;
    }
    else
    {
        // 使用校正后的标称速度
        estimated_distance = corrected_distance;
    }

    // 更新位置
    float new_position;
    if (actuator->direction)
    {
        new_position = actuator->current_position + estimated_distance;
    }
    else
    {
        new_position = actuator->current_position - estimated_distance;
    }

    // 位置边界限制
    float max_stroke = (actuator->type == ACTUATOR_BACK) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
    if (new_position < 0)
        new_position = 0;
    if (new_position > max_stroke)
        new_position = max_stroke;

    // 更新速度估算器
    update_speed_estimator(&actuator->speed_est, new_position, current_time);

    // 更新实际速度
    actuator->actual_speed = get_estimated_speed(&actuator->speed_est);

    // 计算位置误差（如果有外部位置反馈）
    // 这里可以集成编码器或其他位置传感器的反馈
    // float actual_position = read_position_sensor(actuator->type);
    // actuator->position_error = actual_position - new_position;

    // 更新速度校正因子
    if (actuator->speed_est.sample_count >= MIN_SAMPLES)
    {
        float speed_ratio = actuator->actual_speed / actuator->nominal_speed;
        // 限制校正因子范围
        if (speed_ratio > 0.5f && speed_ratio < 2.0f)
        {
            actuator->speed_correction_factor = 0.9f * actuator->speed_correction_factor +
                                                0.1f * speed_ratio;
        }
    }

    // 更新状态
    actuator->current_position = new_position;
    actuator->current_angle = position_to_angle(actuator->type, new_position);
    actuator->last_position = new_position;
    actuator->last_update_time = current_time;

    // 检查是否到达目标
    float position_error = fabs(actuator->current_position - actuator->target_position);
    if (position_error <= POSITION_TOLERANCE)
    {
        actuator->is_moving = false;
        actuator->current_position = actuator->target_position;
        actuator->current_angle = actuator->target_angle;
        // 停止PWM输出
        // set_actuator_pwm(actuator->type, 0, true);
    }
}

/**
 * 自适应角度控制
 */
bool move_actuator_to_angle_adaptive(adaptive_actuator_state_t *actuator, float target_angle)
{
    float max_angle = (actuator->type == ACTUATOR_BACK) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
    if (target_angle < 0 || target_angle > max_angle)
        return false;

    float target_position = angle_to_position(actuator->type, target_angle);
    float distance = fabs(target_position - actuator->current_position);

    if (distance <= POSITION_TOLERANCE)
        return true;

    // 设置运动参数
    actuator->target_angle = target_angle;
    actuator->target_position = target_position;
    actuator->direction = (target_position > actuator->current_position);
    actuator->move_start_time = system_time_ms;
    actuator->last_update_time = system_time_ms;
    actuator->is_moving = true;

    // 启动PWM输出
    // set_actuator_pwm(actuator->type, PWM_MAX, actuator->direction);

    printf("自适应控制启动: 目标%.1f°, 距离%.1fmm, 预估速度%.2fmm/s\n",
           target_angle, distance, actuator->actual_speed);

    return true;
}

/**
 * 初始化自适应控制系统
 */
void adaptive_actuator_init(void)
{
    // 初始化背部推杆
    back_actuator.type = ACTUATOR_BACK;
    back_actuator.current_position = 0;
    back_actuator.current_angle = 0;
    back_actuator.nominal_speed = SPEED_NOMINAL;
    back_actuator.actual_speed = SPEED_NOMINAL;
    back_actuator.speed_correction_factor = 1.0f;
    back_actuator.adaptive_enabled = true;
    init_speed_estimator(&back_actuator.speed_est);

    // 初始化腿部推杆
    leg_actuator.type = ACTUATOR_LEG;
    leg_actuator.current_position = 0;
    leg_actuator.current_angle = 0;
    leg_actuator.nominal_speed = SPEED_NOMINAL;
    leg_actuator.actual_speed = SPEED_NOMINAL;
    leg_actuator.speed_correction_factor = 1.0f;
    leg_actuator.adaptive_enabled = true;
    init_speed_estimator(&leg_actuator.speed_est);

    system_time_ms = 0;

    printf("自适应推杆控制系统初始化完成\n");
}

/**
 * 自适应控制任务（1ms调用）
 */
void adaptive_control_task(void)
{
    system_time_ms++;

    adaptive_position_update(&back_actuator);
    adaptive_position_update(&leg_actuator);
}

/**
 * 设置背部角度（自适应版本）
 */
bool set_back_angle_adaptive(float angle)
{
    return move_actuator_to_angle_adaptive(&back_actuator, angle);
}

/**
 * 设置腿部角度（自适应版本）
 */
bool set_leg_angle_adaptive(float angle)
{
    return move_actuator_to_angle_adaptive(&leg_actuator, angle);
}

/**
 * 获取自适应控制状态
 */
void get_adaptive_status(void)
{
    printf("=== 自适应控制状态 ===\n");
    printf("背部推杆:\n");
    printf("  当前角度: %.2f°\n", back_actuator.current_angle);
    printf("  标称速度: %.2f mm/s\n", back_actuator.nominal_speed);
    printf("  实际速度: %.2f mm/s\n", back_actuator.actual_speed);
    printf("  校正因子: %.3f\n", back_actuator.speed_correction_factor);
    printf("  样本数量: %d\n", back_actuator.speed_est.sample_count);

    printf("腿部推杆:\n");
    printf("  当前角度: %.2f°\n", leg_actuator.current_angle);
    printf("  标称速度: %.2f mm/s\n", leg_actuator.nominal_speed);
    printf("  实际速度: %.2f mm/s\n", leg_actuator.actual_speed);
    printf("  校正因子: %.3f\n", leg_actuator.speed_correction_factor);
    printf("  样本数量: %d\n", leg_actuator.speed_est.sample_count);
}
