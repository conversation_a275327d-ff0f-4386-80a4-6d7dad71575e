#include "actuator_control.h"
#include <stdio.h>
#include <assert.h>
#include <math.h>

// 测试容差
#define TEST_TOLERANCE 0.01f

/**
 * 测试角度到位置的转换
 */
void test_angle_to_position(void) {
    printf("测试角度到位置转换...\n");
    
    // 背部推杆测试
    float pos;
    
    // 测试边界值
    pos = angle_to_position(ACTUATOR_BACK, 0.0f);
    assert(fabs(pos - 0.0f) < TEST_TOLERANCE);
    printf("  背部 0° -> %.2fmm ✓\n", pos);
    
    pos = angle_to_position(ACTUATOR_BACK, 65.0f);
    assert(fabs(pos - 144.0f) < TEST_TOLERANCE);
    printf("  背部 65° -> %.2fmm ✓\n", pos);
    
    // 测试中间值
    pos = angle_to_position(ACTUATOR_BACK, 32.5f);
    assert(fabs(pos - 72.0f) < TEST_TOLERANCE);
    printf("  背部 32.5° -> %.2fmm ✓\n", pos);
    
    // 腿部推杆测试
    pos = angle_to_position(ACTUATOR_LEG, 0.0f);
    assert(fabs(pos - 0.0f) < TEST_TOLERANCE);
    printf("  腿部 0° -> %.2fmm ✓\n", pos);
    
    pos = angle_to_position(ACTUATOR_LEG, 45.0f);
    assert(fabs(pos - 124.0f) < TEST_TOLERANCE);
    printf("  腿部 45° -> %.2fmm ✓\n", pos);
    
    pos = angle_to_position(ACTUATOR_LEG, 22.5f);
    assert(fabs(pos - 62.0f) < TEST_TOLERANCE);
    printf("  腿部 22.5° -> %.2fmm ✓\n", pos);
    
    printf("角度到位置转换测试通过 ✓\n\n");
}

/**
 * 测试位置到角度的转换
 */
void test_position_to_angle(void) {
    printf("测试位置到角度转换...\n");
    
    float angle;
    
    // 背部推杆测试
    angle = position_to_angle(ACTUATOR_BACK, 0.0f);
    assert(fabs(angle - 0.0f) < TEST_TOLERANCE);
    printf("  背部 0mm -> %.2f° ✓\n", angle);
    
    angle = position_to_angle(ACTUATOR_BACK, 144.0f);
    assert(fabs(angle - 65.0f) < TEST_TOLERANCE);
    printf("  背部 144mm -> %.2f° ✓\n", angle);
    
    angle = position_to_angle(ACTUATOR_BACK, 72.0f);
    assert(fabs(angle - 32.5f) < TEST_TOLERANCE);
    printf("  背部 72mm -> %.2f° ✓\n", angle);
    
    // 腿部推杆测试
    angle = position_to_angle(ACTUATOR_LEG, 0.0f);
    assert(fabs(angle - 0.0f) < TEST_TOLERANCE);
    printf("  腿部 0mm -> %.2f° ✓\n", angle);
    
    angle = position_to_angle(ACTUATOR_LEG, 124.0f);
    assert(fabs(angle - 45.0f) < TEST_TOLERANCE);
    printf("  腿部 124mm -> %.2f° ✓\n", angle);
    
    angle = position_to_angle(ACTUATOR_LEG, 62.0f);
    assert(fabs(angle - 22.5f) < TEST_TOLERANCE);
    printf("  腿部 62mm -> %.2f° ✓\n", angle);
    
    printf("位置到角度转换测试通过 ✓\n\n");
}

/**
 * 测试往返转换精度
 */
void test_conversion_accuracy(void) {
    printf("测试往返转换精度...\n");
    
    // 测试多个角度值的往返转换
    float test_angles_back[] = {0, 10, 20, 30, 40, 50, 60, 65};
    float test_angles_leg[] = {0, 5, 10, 15, 20, 25, 30, 35, 40, 45};
    
    // 背部推杆测试
    for (int i = 0; i < sizeof(test_angles_back)/sizeof(test_angles_back[0]); i++) {
        float original_angle = test_angles_back[i];
        float position = angle_to_position(ACTUATOR_BACK, original_angle);
        float converted_angle = position_to_angle(ACTUATOR_BACK, position);
        
        assert(fabs(original_angle - converted_angle) < TEST_TOLERANCE);
        printf("  背部 %.0f° -> %.2fmm -> %.2f° (误差: %.4f°) ✓\n", 
               original_angle, position, converted_angle, 
               fabs(original_angle - converted_angle));
    }
    
    // 腿部推杆测试
    for (int i = 0; i < sizeof(test_angles_leg)/sizeof(test_angles_leg[0]); i++) {
        float original_angle = test_angles_leg[i];
        float position = angle_to_position(ACTUATOR_LEG, original_angle);
        float converted_angle = position_to_angle(ACTUATOR_LEG, position);
        
        assert(fabs(original_angle - converted_angle) < TEST_TOLERANCE);
        printf("  腿部 %.0f° -> %.2fmm -> %.2f° (误差: %.4f°) ✓\n", 
               original_angle, position, converted_angle, 
               fabs(original_angle - converted_angle));
    }
    
    printf("往返转换精度测试通过 ✓\n\n");
}

/**
 * 测试运动时间计算
 */
void test_movement_timing(void) {
    printf("测试运动时间计算...\n");
    
    // 测试不同距离和速度的运动时间
    struct {
        float distance;  // mm
        float speed;     // mm/s
        float expected_time; // s
    } test_cases[] = {
        {144.0f, 3.8f, 37.89f},  // 背部全程，负载速度
        {124.0f, 3.8f, 32.63f},  // 腿部全程，负载速度
        {72.0f, 4.2f, 17.14f},   // 背部半程，空载速度
        {62.0f, 4.2f, 14.76f},   // 腿部半程，空载速度
    };
    
    for (int i = 0; i < sizeof(test_cases)/sizeof(test_cases[0]); i++) {
        float calculated_time = test_cases[i].distance / test_cases[i].speed;
        float error = fabs(calculated_time - test_cases[i].expected_time);
        
        printf("  距离%.0fmm, 速度%.1fmm/s -> 时间%.2fs (预期%.2fs, 误差%.2fs) ",
               test_cases[i].distance, test_cases[i].speed, 
               calculated_time, test_cases[i].expected_time, error);
        
        if (error < 0.1f) {
            printf("✓\n");
        } else {
            printf("⚠\n");
        }
    }
    
    printf("运动时间计算测试完成\n\n");
}

/**
 * 测试边界条件
 */
void test_boundary_conditions(void) {
    printf("测试边界条件...\n");
    
    // 测试超出范围的角度
    float pos;
    
    // 负角度应该被限制为0
    pos = angle_to_position(ACTUATOR_BACK, -10.0f);
    assert(fabs(pos - 0.0f) < TEST_TOLERANCE);
    printf("  背部 -10° -> %.2fmm (限制为0) ✓\n", pos);
    
    // 超出最大角度应该被限制
    pos = angle_to_position(ACTUATOR_BACK, 80.0f);
    assert(fabs(pos - 144.0f) < TEST_TOLERANCE);
    printf("  背部 80° -> %.2fmm (限制为最大值) ✓\n", pos);
    
    pos = angle_to_position(ACTUATOR_LEG, 60.0f);
    assert(fabs(pos - 124.0f) < TEST_TOLERANCE);
    printf("  腿部 60° -> %.2fmm (限制为最大值) ✓\n", pos);
    
    // 测试超出范围的位置
    float angle;
    
    // 负位置应该被限制为0
    angle = position_to_angle(ACTUATOR_BACK, -10.0f);
    assert(fabs(angle - 0.0f) < TEST_TOLERANCE);
    printf("  背部 -10mm -> %.2f° (限制为0) ✓\n", angle);
    
    // 超出最大位置应该被限制
    angle = position_to_angle(ACTUATOR_BACK, 200.0f);
    assert(fabs(angle - 65.0f) < TEST_TOLERANCE);
    printf("  背部 200mm -> %.2f° (限制为最大值) ✓\n", angle);
    
    angle = position_to_angle(ACTUATOR_LEG, 150.0f);
    assert(fabs(angle - 45.0f) < TEST_TOLERANCE);
    printf("  腿部 150mm -> %.2f° (限制为最大值) ✓\n", angle);
    
    printf("边界条件测试通过 ✓\n\n");
}

/**
 * 测试角度分辨率
 */
void test_angle_resolution(void) {
    printf("测试角度分辨率...\n");
    
    // 计算理论分辨率
    float back_resolution = 65.0f / 144.0f;  // 度/mm
    float leg_resolution = 45.0f / 124.0f;   // 度/mm
    
    printf("  背部推杆理论分辨率: %.4f°/mm\n", back_resolution);
    printf("  腿部推杆理论分辨率: %.4f°/mm\n", leg_resolution);
    
    // 测试1mm位移对应的角度变化
    float back_angle_1mm = position_to_angle(ACTUATOR_BACK, 1.0f);
    float leg_angle_1mm = position_to_angle(ACTUATOR_LEG, 1.0f);
    
    printf("  背部推杆1mm对应角度: %.4f°\n", back_angle_1mm);
    printf("  腿部推杆1mm对应角度: %.4f°\n", leg_angle_1mm);
    
    // 验证分辨率计算
    assert(fabs(back_angle_1mm - back_resolution) < 0.001f);
    assert(fabs(leg_angle_1mm - leg_resolution) < 0.001f);
    
    printf("角度分辨率测试通过 ✓\n\n");
}

/**
 * 性能测试
 */
void test_performance(void) {
    printf("测试性能...\n");
    
    // 测试转换函数的执行时间
    const int iterations = 10000;
    
    printf("  执行%d次角度到位置转换...\n", iterations);
    for (int i = 0; i < iterations; i++) {
        float angle = (float)(i % 66);  // 0-65度
        angle_to_position(ACTUATOR_BACK, angle);
    }
    printf("  完成 ✓\n");
    
    printf("  执行%d次位置到角度转换...\n", iterations);
    for (int i = 0; i < iterations; i++) {
        float position = (float)(i % 145);  // 0-144mm
        position_to_angle(ACTUATOR_BACK, position);
    }
    printf("  完成 ✓\n");
    
    printf("性能测试完成\n\n");
}

/**
 * 运行所有测试
 */
int main(void) {
    printf("=== 推杆控制算法测试 ===\n\n");
    
    // 运行各项测试
    test_angle_to_position();
    test_position_to_angle();
    test_conversion_accuracy();
    test_movement_timing();
    test_boundary_conditions();
    test_angle_resolution();
    test_performance();
    
    printf("=== 所有测试通过 ✓ ===\n");
    printf("算法验证完成，可以安全使用。\n");
    
    return 0;
}
