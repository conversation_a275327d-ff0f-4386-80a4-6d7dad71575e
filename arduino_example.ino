/*
 * Arduino推杆控制系统示例
 * 基于时间的角度控制算法
 * 
 * 硬件连接：
 * - 背部推杆：引脚9(PWM A), 引脚10(PWM B)
 * - 腿部推杆：引脚11(PWM A), 引脚12(PWM B)
 * - 使能引脚：引脚13
 */

// 推杆参数
#define BACK_MAX_STROKE     144.0f    // 背部最大行程 (mm)
#define LEG_MAX_STROKE      124.0f    // 腿部最大行程 (mm)
#define BACK_MAX_ANGLE      65.0f     // 背部最大角度 (度)
#define LEG_MAX_ANGLE       45.0f     // 腿部最大角度 (度)

// 速度参数 (mm/s)
#define LOAD_SPEED          3.8f      // 负载速度
#define NOLOAD_SPEED        4.2f      // 空载速度

// 硬件引脚定义
#define BACK_PWM_A_PIN      9
#define BACK_PWM_B_PIN      10
#define LEG_PWM_A_PIN       11
#define LEG_PWM_B_PIN       12
#define MOTOR_ENABLE_PIN    13

// 控制参数
#define PWM_MAX             255       // Arduino PWM最大值
#define POSITION_TOLERANCE  0.5f      // 位置容差 (mm)

// 推杆类型
enum ActuatorType {
  ACTUATOR_BACK = 0,
  ACTUATOR_LEG = 1
};

// 推杆状态结构体
struct ActuatorState {
  ActuatorType type;
  float current_position;     // 当前位置 (mm)
  float target_position;      // 目标位置 (mm)
  float current_angle;        // 当前角度 (度)
  float target_angle;         // 目标角度 (度)
  float speed;                // 当前速度 (mm/s)
  bool is_moving;             // 是否在运动
  bool direction;             // 运动方向 (true=伸出, false=收回)
  unsigned long move_start_time;  // 运动开始时间 (ms)
  unsigned long estimated_time;   // 预估运动时间 (ms)
};

// 全局变量
ActuatorState back_actuator = {ACTUATOR_BACK, 0, 0, 0, 0, 0, false, true, 0, 0};
ActuatorState leg_actuator = {ACTUATOR_LEG, 0, 0, 0, 0, 0, false, true, 0, 0};

/**
 * 角度转换为位置
 */
float angleToPosition(ActuatorType type, float angle) {
  float max_stroke = (type == ACTUATOR_BACK) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
  float max_angle = (type == ACTUATOR_BACK) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
  
  // 限制角度范围
  if (angle < 0) angle = 0;
  if (angle > max_angle) angle = max_angle;
  
  // 线性映射：角度 -> 位置
  return (angle / max_angle) * max_stroke;
}

/**
 * 位置转换为角度
 */
float positionToAngle(ActuatorType type, float position) {
  float max_stroke = (type == ACTUATOR_BACK) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
  float max_angle = (type == ACTUATOR_BACK) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
  
  // 限制位置范围
  if (position < 0) position = 0;
  if (position > max_stroke) position = max_stroke;
  
  // 线性映射：位置 -> 角度
  return (position / max_stroke) * max_angle;
}

/**
 * 设置推杆PWM输出
 */
void setActuatorPWM(ActuatorType type, int pwm_value, bool direction) {
  if (type == ACTUATOR_BACK) {
    if (direction) {
      // 伸出方向
      analogWrite(BACK_PWM_A_PIN, pwm_value);
      analogWrite(BACK_PWM_B_PIN, 0);
    } else {
      // 收回方向
      analogWrite(BACK_PWM_A_PIN, 0);
      analogWrite(BACK_PWM_B_PIN, pwm_value);
    }
  } else {
    if (direction) {
      // 伸出方向
      analogWrite(LEG_PWM_A_PIN, pwm_value);
      analogWrite(LEG_PWM_B_PIN, 0);
    } else {
      // 收回方向
      analogWrite(LEG_PWM_A_PIN, 0);
      analogWrite(LEG_PWM_B_PIN, pwm_value);
    }
  }
}

/**
 * 更新推杆位置
 */
void updateActuatorPosition(ActuatorState* actuator) {
  if (!actuator->is_moving) return;
  
  unsigned long elapsed_time = millis() - actuator->move_start_time;
  float distance_moved = (actuator->speed * elapsed_time) / 1000.0f;
  
  if (actuator->direction) {
    // 伸出方向
    actuator->current_position += distance_moved;
  } else {
    // 收回方向
    actuator->current_position -= distance_moved;
  }
  
  // 限制位置范围
  float max_stroke = (actuator->type == ACTUATOR_BACK) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
  if (actuator->current_position < 0) actuator->current_position = 0;
  if (actuator->current_position > max_stroke) actuator->current_position = max_stroke;
  
  // 更新当前角度
  actuator->current_angle = positionToAngle(actuator->type, actuator->current_position);
  
  // 检查是否到达目标位置
  float position_error = abs(actuator->current_position - actuator->target_position);
  if (position_error <= POSITION_TOLERANCE || elapsed_time >= actuator->estimated_time) {
    // 停止运动
    actuator->is_moving = false;
    actuator->current_position = actuator->target_position;
    actuator->current_angle = actuator->target_angle;
    setActuatorPWM(actuator->type, 0, true);
  }
}

/**
 * 移动推杆到指定角度
 */
bool moveActuatorToAngle(ActuatorState* actuator, float target_angle, float speed) {
  // 检查参数有效性
  float max_angle = (actuator->type == ACTUATOR_BACK) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
  if (target_angle < 0 || target_angle > max_angle) return false;
  if (speed <= 0) return false;
  
  // 计算目标位置
  float target_position = angleToPosition(actuator->type, target_angle);
  float distance = abs(target_position - actuator->current_position);
  
  // 如果已经在目标位置，不需要移动
  if (distance <= POSITION_TOLERANCE) return true;
  
  // 设置运动参数
  actuator->target_angle = target_angle;
  actuator->target_position = target_position;
  actuator->speed = speed;
  actuator->direction = (target_position > actuator->current_position);
  actuator->move_start_time = millis();
  actuator->estimated_time = (unsigned long)((distance / speed) * 1000);
  actuator->is_moving = true;
  
  // 启动PWM输出
  setActuatorPWM(actuator->type, PWM_MAX, actuator->direction);
  
  return true;
}

/**
 * 设置背部角度
 */
bool setBackAngle(float angle, float speed = LOAD_SPEED) {
  return moveActuatorToAngle(&back_actuator, angle, speed);
}

/**
 * 设置腿部角度
 */
bool setLegAngle(float angle, float speed = LOAD_SPEED) {
  return moveActuatorToAngle(&leg_actuator, angle, speed);
}

/**
 * 获取背部当前角度
 */
float getBackAngle() {
  return back_actuator.current_angle;
}

/**
 * 获取腿部当前角度
 */
float getLegAngle() {
  return leg_actuator.current_angle;
}

/**
 * 检查推杆是否在运动
 */
bool isActuatorMoving(ActuatorType type) {
  if (type == ACTUATOR_BACK) {
    return back_actuator.is_moving;
  } else {
    return leg_actuator.is_moving;
  }
}

/**
 * 紧急停止
 */
void emergencyStop() {
  back_actuator.is_moving = false;
  leg_actuator.is_moving = false;
  setActuatorPWM(ACTUATOR_BACK, 0, true);
  setActuatorPWM(ACTUATOR_LEG, 0, true);
}

/**
 * Arduino初始化
 */
void setup() {
  // 初始化串口
  Serial.begin(115200);
  Serial.println("推杆控制系统启动...");
  
  // 初始化引脚
  pinMode(BACK_PWM_A_PIN, OUTPUT);
  pinMode(BACK_PWM_B_PIN, OUTPUT);
  pinMode(LEG_PWM_A_PIN, OUTPUT);
  pinMode(LEG_PWM_B_PIN, OUTPUT);
  pinMode(MOTOR_ENABLE_PIN, OUTPUT);
  
  // 使能电机
  digitalWrite(MOTOR_ENABLE_PIN, HIGH);
  
  // 初始化推杆状态
  back_actuator.current_position = 0;
  back_actuator.current_angle = 0;
  leg_actuator.current_position = 0;
  leg_actuator.current_angle = 0;
  
  Serial.println("系统初始化完成");
  
  // 演示控制序列
  demonstrateControl();
}

/**
 * Arduino主循环
 */
void loop() {
  // 更新推杆位置（相当于1ms定时器任务）
  updateActuatorPosition(&back_actuator);
  updateActuatorPosition(&leg_actuator);
  
  // 检查串口命令
  if (Serial.available()) {
    processSerialCommand();
  }
  
  delay(1);  // 1ms延时
}

/**
 * 演示控制序列
 */
void demonstrateControl() {
  Serial.println("开始演示控制序列...");
  
  // 测试1：背部推杆移动到30度
  Serial.println("背部推杆移动到30度");
  setBackAngle(30.0);
  
  // 等待运动完成
  while (isActuatorMoving(ACTUATOR_BACK)) {
    updateActuatorPosition(&back_actuator);
    delay(10);
  }
  Serial.print("背部角度: ");
  Serial.println(getBackAngle());
  
  // 测试2：腿部推杆移动到25度
  Serial.println("腿部推杆移动到25度");
  setLegAngle(25.0);
  
  while (isActuatorMoving(ACTUATOR_LEG)) {
    updateActuatorPosition(&leg_actuator);
    delay(10);
  }
  Serial.print("腿部角度: ");
  Serial.println(getLegAngle());
  
  // 测试3：同时控制
  Serial.println("同时控制两个推杆");
  setBackAngle(50.0);
  setLegAngle(35.0);
  
  while (isActuatorMoving(ACTUATOR_BACK) || isActuatorMoving(ACTUATOR_LEG)) {
    updateActuatorPosition(&back_actuator);
    updateActuatorPosition(&leg_actuator);
    
    Serial.print("背部: ");
    Serial.print(getBackAngle());
    Serial.print("度, 腿部: ");
    Serial.print(getLegAngle());
    Serial.println("度");
    
    delay(100);
  }
  
  Serial.println("演示完成");
}

/**
 * 处理串口命令
 */
void processSerialCommand() {
  String command = Serial.readStringUntil('\n');
  command.trim();
  
  if (command.startsWith("back ")) {
    float angle = command.substring(5).toFloat();
    if (setBackAngle(angle)) {
      Serial.print("背部推杆移动到 ");
      Serial.print(angle);
      Serial.println(" 度");
    } else {
      Serial.println("无效的背部角度");
    }
  }
  else if (command.startsWith("leg ")) {
    float angle = command.substring(4).toFloat();
    if (setLegAngle(angle)) {
      Serial.print("腿部推杆移动到 ");
      Serial.print(angle);
      Serial.println(" 度");
    } else {
      Serial.println("无效的腿部角度");
    }
  }
  else if (command == "stop") {
    emergencyStop();
    Serial.println("紧急停止");
  }
  else if (command == "status") {
    Serial.print("背部: ");
    Serial.print(getBackAngle());
    Serial.print("度, 腿部: ");
    Serial.print(getLegAngle());
    Serial.println("度");
  }
  else if (command == "help") {
    Serial.println("可用命令:");
    Serial.println("  back <角度>  - 设置背部角度 (0-65度)");
    Serial.println("  leg <角度>   - 设置腿部角度 (0-45度)");
    Serial.println("  stop        - 紧急停止");
    Serial.println("  status      - 显示当前状态");
    Serial.println("  help        - 显示帮助");
  }
  else {
    Serial.println("未知命令，输入 'help' 查看帮助");
  }
}
