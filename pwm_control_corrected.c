#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <math.h>

/**
 * 修正版PWM推杆控制系统
 * PWM占空比：0-100%
 */

// 推杆参数
#define BACK_MAX_STROKE     144.0f    // 背部最大行程 (mm)
#define LEG_MAX_STROKE      124.0f    // 腿部最大行程 (mm)
#define BACK_MAX_ANGLE      65.0f     // 背部最大角度 (度)
#define LEG_MAX_ANGLE       45.0f     // 腿部最大角度 (度)

// PWM参数 (修正为0-100%)
#define PWM_MAX             100.0f    // PWM最大占空比 (%)
#define PWM_MIN             0.0f      // PWM最小占空比 (%)
#define PWM_DEAD_ZONE       15.0f     // PWM死区 (%)
#define PWM_FREQUENCY       1000      // PWM频率 (Hz)

// 电机特性参数
#define MOTOR_VOLTAGE       29.0f     // 电机额定电压 (V)
#define MOTOR_MAX_SPEED     4.2f      // 最大速度 (mm/s) @ 100% PWM
#define MOTOR_MIN_SPEED     0.6f      // 最小速度 (mm/s) @ 15% PWM

// 推杆控制状态
typedef struct {
    float current_position;      // 当前位置 (mm)
    float target_position;       // 目标位置 (mm)
    float current_angle;         // 当前角度 (度)
    float target_angle;          // 目标角度 (度)
    
    float current_pwm;           // 当前PWM占空比 (%)
    bool direction;              // 运动方向 (true=伸出, false=收回)
    bool is_moving;              // 是否在运动
    
    float calculated_speed;      // 计算速度 (mm/s)
    float load_factor;           // 负载系数
    
    uint32_t last_update_time;   // 上次更新时间 (ms)
    float position_tolerance;    // 位置容差 (mm)
} actuator_state_t;

static actuator_state_t back_actuator = {0};
static actuator_state_t leg_actuator = {0};
static uint32_t system_time_ms = 0;

/**
 * PWM占空比到速度的映射
 * 输入：PWM占空比 (0-100%)
 * 输出：推杆速度 (mm/s)
 */
float pwm_to_speed(float pwm_percent, float load_factor) {
    // 死区检查
    if (pwm_percent < PWM_DEAD_ZONE) {
        return 0.0f;
    }
    
    // 限制PWM范围
    if (pwm_percent > PWM_MAX) pwm_percent = PWM_MAX;
    
    // 线性映射：PWM% -> 速度
    // 15% PWM -> 0.6 mm/s
    // 100% PWM -> 4.2 mm/s
    float speed_range = MOTOR_MAX_SPEED - MOTOR_MIN_SPEED;
    float pwm_range = PWM_MAX - PWM_DEAD_ZONE;
    float pwm_normalized = (pwm_percent - PWM_DEAD_ZONE) / pwm_range;
    
    float base_speed = MOTOR_MIN_SPEED + speed_range * pwm_normalized;
    
    // 负载补偿
    float load_compensation = 1.0f - (load_factor * 0.25f);
    float actual_speed = base_speed * load_compensation;
    
    return actual_speed;
}

/**
 * 速度到PWM占空比的反向映射
 * 输入：目标速度 (mm/s)
 * 输出：PWM占空比 (%)
 */
float speed_to_pwm(float target_speed, float load_factor) {
    if (target_speed <= 0) {
        return 0.0f;
    }
    
    // 负载补偿
    float load_compensation = 1.0f - (load_factor * 0.25f);
    float required_base_speed = target_speed / load_compensation;
    
    // 限制速度范围
    if (required_base_speed < MOTOR_MIN_SPEED) {
        required_base_speed = MOTOR_MIN_SPEED;
    }
    if (required_base_speed > MOTOR_MAX_SPEED) {
        required_base_speed = MOTOR_MAX_SPEED;
    }
    
    // 反向映射：速度 -> PWM%
    float speed_range = MOTOR_MAX_SPEED - MOTOR_MIN_SPEED;
    float pwm_range = PWM_MAX - PWM_DEAD_ZONE;
    float speed_normalized = (required_base_speed - MOTOR_MIN_SPEED) / speed_range;
    
    float pwm_percent = PWM_DEAD_ZONE + pwm_range * speed_normalized;
    
    return pwm_percent;
}

/**
 * 计算负载系数
 */
float calculate_load_factor(float angle, bool direction) {
    // 重力负载随角度变化
    float gravity_component = 800.0f * sin(angle * M_PI / 180.0f);
    float friction_force = 120.0f;  // 基础摩擦力
    
    float total_load;
    if (direction) {
        // 伸出：对抗重力
        total_load = gravity_component + friction_force;
    } else {
        // 收回：重力助力
        total_load = friction_force - gravity_component * 0.5f;
        if (total_load < friction_force * 0.3f) {
            total_load = friction_force * 0.3f;
        }
    }
    
    // 负载系数 (0-1)
    float load_factor = total_load / 6000.0f;
    if (load_factor < 0.05f) load_factor = 0.05f;
    if (load_factor > 0.6f) load_factor = 0.6f;
    
    return load_factor;
}

/**
 * 设置PWM输出
 */
void set_pwm_output(actuator_state_t* actuator, float pwm_percent) {
    actuator->current_pwm = pwm_percent;
    
    // 这里调用实际的硬件PWM设置函数
    printf("设置PWM: %.1f%%, 方向: %s\n", 
           pwm_percent, actuator->direction ? "伸出" : "收回");
}

/**
 * 位置更新（时间积分）
 */
void update_position(actuator_state_t* actuator) {
    if (!actuator->is_moving || actuator->current_pwm == 0) {
        return;
    }
    
    uint32_t current_time = system_time_ms;
    uint32_t elapsed_time = current_time - actuator->last_update_time;
    
    if (elapsed_time == 0) return;
    
    // 计算当前速度
    actuator->calculated_speed = pwm_to_speed(actuator->current_pwm, actuator->load_factor);
    
    // 时间积分计算位移
    float distance_moved = (actuator->calculated_speed * elapsed_time) / 1000.0f;
    
    // 更新位置
    if (actuator->direction) {
        actuator->current_position += distance_moved;
    } else {
        actuator->current_position -= distance_moved;
    }
    
    // 边界限制
    float max_stroke = (actuator == &back_actuator) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
    if (actuator->current_position < 0) actuator->current_position = 0;
    if (actuator->current_position > max_stroke) actuator->current_position = max_stroke;
    
    // 更新角度
    float max_angle = (actuator == &back_actuator) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
    actuator->current_angle = (actuator->current_position / max_stroke) * max_angle;
    
    actuator->last_update_time = current_time;
    
    // 检查是否到达目标
    float position_error = fabs(actuator->current_position - actuator->target_position);
    if (position_error <= actuator->position_tolerance) {
        actuator->is_moving = false;
        actuator->current_position = actuator->target_position;
        actuator->current_angle = actuator->target_angle;
        set_pwm_output(actuator, 0.0f);
        
        printf("✅ 到达目标位置 %.1f°\n", actuator->current_angle);
    }
}

/**
 * 移动到目标角度
 */
bool move_to_angle(actuator_state_t* actuator, float target_angle) {
    float max_angle = (actuator == &back_actuator) ? BACK_MAX_ANGLE : LEG_MAX_ANGLE;
    float max_stroke = (actuator == &back_actuator) ? BACK_MAX_STROKE : LEG_MAX_STROKE;
    
    if (target_angle < 0 || target_angle > max_angle) return false;
    
    float target_position = (target_angle / max_angle) * max_stroke;
    float distance = fabs(target_position - actuator->current_position);
    
    if (distance <= actuator->position_tolerance) return true;
    
    // 设置目标
    actuator->target_angle = target_angle;
    actuator->target_position = target_position;
    actuator->direction = (target_position > actuator->current_position);
    actuator->is_moving = true;
    actuator->last_update_time = system_time_ms;
    
    // 计算负载系数
    actuator->load_factor = calculate_load_factor(actuator->current_angle, actuator->direction);
    
    // 根据距离确定速度和PWM
    float target_speed;
    if (distance > 20.0f) {
        target_speed = 3.5f;  // 高速
    } else if (distance > 5.0f) {
        target_speed = 2.0f;  // 中速
    } else {
        target_speed = 1.0f;  // 低速
    }
    
    float required_pwm = speed_to_pwm(target_speed, actuator->load_factor);
    set_pwm_output(actuator, required_pwm);
    
    printf("🎯 开始移动: %.1f° -> %.1f° (PWM: %.1f%%, 速度: %.2f mm/s)\n",
           actuator->current_angle, target_angle, required_pwm, target_speed);
    
    return true;
}

/**
 * 动态PWM调整
 */
void dynamic_pwm_adjustment(actuator_state_t* actuator) {
    if (!actuator->is_moving) return;
    
    float remaining_distance = fabs(actuator->target_position - actuator->current_position);
    
    // 动态速度调整
    float target_speed;
    if (remaining_distance > 15.0f) {
        target_speed = 3.5f;      // 远距离：高速
    } else if (remaining_distance > 5.0f) {
        target_speed = 2.0f;      // 中距离：中速
    } else if (remaining_distance > 1.0f) {
        target_speed = 1.0f;      // 近距离：低速
    } else {
        target_speed = 0.5f;      // 极近距离：极低速
    }
    
    // 重新计算负载系数
    actuator->load_factor = calculate_load_factor(actuator->current_angle, actuator->direction);
    
    // 计算新PWM
    float new_pwm = speed_to_pwm(target_speed, actuator->load_factor);
    
    // 只有变化较大时才更新
    if (fabs(new_pwm - actuator->current_pwm) > 3.0f) {
        set_pwm_output(actuator, new_pwm);
    }
}

/**
 * 初始化系统
 */
void pwm_system_init(void) {
    back_actuator.position_tolerance = 0.5f;
    leg_actuator.position_tolerance = 0.5f;
    
    printf("🚀 PWM推杆控制系统初始化\n");
    printf("PWM范围: 0-100%%\n");
    printf("死区: %.0f%%\n", PWM_DEAD_ZONE);
    printf("速度范围: %.1f-%.1f mm/s\n", MOTOR_MIN_SPEED, MOTOR_MAX_SPEED);
}

/**
 * 控制任务 (1ms调用)
 */
void pwm_control_task(void) {
    system_time_ms++;
    
    // 更新位置
    update_position(&back_actuator);
    update_position(&leg_actuator);
    
    // 动态PWM调整 (每10ms)
    if (system_time_ms % 10 == 0) {
        dynamic_pwm_adjustment(&back_actuator);
        dynamic_pwm_adjustment(&leg_actuator);
    }
}

/**
 * PWM控制演示
 */
void pwm_demo(void) {
    printf("\n📊 PWM控制演示 (0-100%%)\n");
    printf("========================\n");
    
    // 测试不同PWM值对应的速度
    printf("PWM(%%) | 速度(mm/s) | 说明\n");
    printf("-------|-----------|------\n");
    
    float test_pwm[] = {0, 10, 15, 25, 50, 75, 100};
    
    for (int i = 0; i < 7; i++) {
        float pwm = test_pwm[i];
        float speed = pwm_to_speed(pwm, 0.2f);  // 假设20%负载
        
        const char* desc;
        if (pwm == 0) desc = "停止";
        else if (pwm < PWM_DEAD_ZONE) desc = "死区";
        else if (pwm < 30) desc = "低速";
        else if (pwm < 70) desc = "中速";
        else desc = "高速";
        
        printf("  %3.0f  |   %.2f    | %s\n", pwm, speed, desc);
    }
    
    printf("\n🎯 PWM控制特点：\n");
    printf("• 0-100%% 直观的占空比表示\n");
    printf("• 15%% 以下为死区，电机不转动\n");
    printf("• 线性映射关系，易于计算\n");
    printf("• 支持负载自适应补偿\n");
}

/**
 * 主程序
 */
int main(void) {
    printf("⚡ PWM推杆控制系统 (修正版)\n");
    printf("===========================\n");
    
    pwm_system_init();
    pwm_demo();
    
    printf("\n🔧 测试运动控制:\n");
    
    // 测试背部推杆运动
    move_to_angle(&back_actuator, 30.0f);
    
    // 模拟运行
    for (int i = 0; i < 5000; i++) {
        pwm_control_task();
        
        if (i % 1000 == 0) {
            printf("时间: %ds, 角度: %.1f°, PWM: %.1f%%, 速度: %.2f mm/s\n",
                   i/1000, back_actuator.current_angle, 
                   back_actuator.current_pwm, back_actuator.calculated_speed);
        }
        
        if (!back_actuator.is_moving) break;
    }
    
    printf("\n✅ PWM控制演示完成！\n");
    printf("PWM占空比 0-100%% 提供了直观、精确的电机控制方式。\n");
    
    return 0;
}
