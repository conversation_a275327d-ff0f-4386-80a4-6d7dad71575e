#ifndef ACTUATOR_CONFIG_H
#define ACTUATOR_CONFIG_H

/**
 * 推杆控制系统配置文件
 * 用户可以根据实际硬件参数修改这些配置
 */

// ============================================================================
// 硬件参数配置
// ============================================================================

// 推杆物理参数
#define CONFIG_BACK_MAX_STROKE      144.0f    // 背部推杆最大行程 (mm)
#define CONFIG_LEG_MAX_STROKE       124.0f    // 腿部推杆最大行程 (mm)
#define CONFIG_BACK_MAX_ANGLE       65.0f     // 背部最大角度 (度)
#define CONFIG_LEG_MAX_ANGLE        45.0f     // 腿部最大角度 (度)

// 电机速度参数 (mm/s)
#define CONFIG_RATED_VOLTAGE        29.0f     // 额定电压 (V)
#define CONFIG_RATED_CURRENT        1.5f      // 额定电流 (A)
#define CONFIG_MAX_FORCE            6000.0f   // 最大推力 (N)
#define CONFIG_LOAD_SPEED_NOM       3.8f      // 负载额定速度 (mm/s)
#define CONFIG_NOLOAD_SPEED_NOM     4.2f      // 空载额定速度 (mm/s)
#define CONFIG_SPEED_TOLERANCE      0.1f      // 速度容差 (±10%)

// 计算得出的速度范围
#define CONFIG_LOAD_SPEED_MIN       (CONFIG_LOAD_SPEED_NOM * (1.0f - CONFIG_SPEED_TOLERANCE))
#define CONFIG_LOAD_SPEED_MAX       (CONFIG_LOAD_SPEED_NOM * (1.0f + CONFIG_SPEED_TOLERANCE))
#define CONFIG_NOLOAD_SPEED_MIN     (CONFIG_NOLOAD_SPEED_NOM * (1.0f - CONFIG_SPEED_TOLERANCE))
#define CONFIG_NOLOAD_SPEED_MAX     (CONFIG_NOLOAD_SPEED_NOM * (1.0f + CONFIG_SPEED_TOLERANCE))

// ============================================================================
// 控制参数配置
// ============================================================================

// PWM参数
#define CONFIG_PWM_FREQUENCY        20000     // PWM频率 (Hz)
#define CONFIG_PWM_RESOLUTION       1000      // PWM分辨率 (0-1000)
#define CONFIG_PWM_DEADTIME         50        // PWM死区时间 (us)

// 控制周期
#define CONFIG_CONTROL_FREQUENCY    1000      // 控制频率 (Hz)
#define CONFIG_CONTROL_PERIOD_MS    1         // 控制周期 (ms)

// 位置控制参数
#define CONFIG_POSITION_TOLERANCE   0.5f      // 位置容差 (mm)
#define CONFIG_ANGLE_TOLERANCE      0.3f      // 角度容差 (度)
#define CONFIG_VELOCITY_TOLERANCE   0.2f      // 速度容差 (mm/s)

// 安全参数
#define CONFIG_MAX_MOVE_TIME        60000     // 最大运动时间 (ms)
#define CONFIG_EMERGENCY_STOP_TIME  100       // 紧急停止时间 (ms)
#define CONFIG_OVERCURRENT_LIMIT    2.0f      // 过流保护限制 (A)

// ============================================================================
// 硬件接口配置
// ============================================================================

// GPIO引脚定义（根据实际硬件修改）
#define CONFIG_BACK_PWM_A_PIN       9         // 背部推杆PWM A
#define CONFIG_BACK_PWM_B_PIN       10        // 背部推杆PWM B
#define CONFIG_LEG_PWM_A_PIN        11        // 腿部推杆PWM A
#define CONFIG_LEG_PWM_B_PIN        12        // 腿部推杆PWM B

// 限位开关引脚（可选）
#define CONFIG_BACK_LIMIT_MIN_PIN   2         // 背部最小限位
#define CONFIG_BACK_LIMIT_MAX_PIN   3         // 背部最大限位
#define CONFIG_LEG_LIMIT_MIN_PIN    4         // 腿部最小限位
#define CONFIG_LEG_LIMIT_MAX_PIN    5         // 腿部最大限位

// 使能引脚
#define CONFIG_MOTOR_ENABLE_PIN     13        // 电机使能引脚

// ============================================================================
// 调试和诊断配置
// ============================================================================

// 调试选项
#define CONFIG_DEBUG_ENABLED        1         // 启用调试输出
#define CONFIG_DEBUG_POSITION       1         // 调试位置信息
#define CONFIG_DEBUG_SPEED          1         // 调试速度信息
#define CONFIG_DEBUG_PWM            0         // 调试PWM信息

// 串口配置
#define CONFIG_UART_BAUDRATE        115200    // 串口波特率
#define CONFIG_UART_BUFFER_SIZE     256       // 串口缓冲区大小

// 数据记录
#define CONFIG_LOG_ENABLED          0         // 启用数据记录
#define CONFIG_LOG_INTERVAL_MS      10        // 记录间隔 (ms)
#define CONFIG_LOG_BUFFER_SIZE      1000      // 记录缓冲区大小

// ============================================================================
// 高级配置
// ============================================================================

// 滤波器参数
#define CONFIG_POSITION_FILTER_ALPHA    0.8f  // 位置滤波系数
#define CONFIG_SPEED_FILTER_ALPHA       0.7f  // 速度滤波系数

// 预测控制参数
#define CONFIG_PREDICTION_ENABLED       0     // 启用位置预测
#define CONFIG_PREDICTION_HORIZON_MS    50    // 预测时间窗口 (ms)

// 自适应控制参数
#define CONFIG_ADAPTIVE_SPEED_ENABLED   0     // 启用自适应速度
#define CONFIG_LOAD_DETECTION_ENABLED   0     // 启用负载检测

// 校准参数
#define CONFIG_AUTO_CALIBRATION         0     // 启用自动校准
#define CONFIG_CALIBRATION_SPEED        1.0f  // 校准速度 (mm/s)
#define CONFIG_CALIBRATION_FORCE        100.0f // 校准力 (N)

// ============================================================================
// 平台特定配置
// ============================================================================

// 选择目标平台（只能选择一个）
#define CONFIG_PLATFORM_STM32           0     // STM32平台
#define CONFIG_PLATFORM_ARDUINO         0     // Arduino平台
#define CONFIG_PLATFORM_ESP32           0     // ESP32平台
#define CONFIG_PLATFORM_GENERIC         1     // 通用平台

// STM32特定配置
#if CONFIG_PLATFORM_STM32
#define CONFIG_STM32_TIMER              TIM2
#define CONFIG_STM32_PWM_TIMER          TIM3
#define CONFIG_STM32_ADC                ADC1
#endif

// Arduino特定配置
#if CONFIG_PLATFORM_ARDUINO
#define CONFIG_ARDUINO_TIMER_PRESCALER  64
#define CONFIG_ARDUINO_PWM_PINS         {9, 10, 11, 12}
#endif

// ESP32特定配置
#if CONFIG_PLATFORM_ESP32
#define CONFIG_ESP32_PWM_CHANNEL_0      0
#define CONFIG_ESP32_PWM_CHANNEL_1      1
#define CONFIG_ESP32_PWM_CHANNEL_2      2
#define CONFIG_ESP32_PWM_CHANNEL_3      3
#endif

// ============================================================================
// 配置验证
// ============================================================================

// 编译时检查配置的有效性（整数参数）
#if CONFIG_CONTROL_FREQUENCY < 100 || CONFIG_CONTROL_FREQUENCY > 10000
#error "控制频率必须在100-10000Hz之间"
#endif

// 确保只选择了一个平台
#if (CONFIG_PLATFORM_STM32 + CONFIG_PLATFORM_ARDUINO + CONFIG_PLATFORM_ESP32 + CONFIG_PLATFORM_GENERIC) != 1
#error "必须且只能选择一个目标平台"
#endif

// 注意：浮点数参数的验证需要在运行时进行，不能在预处理时检查

#endif // ACTUATOR_CONFIG_H
